msgid ""
msgstr ""
"POT-Creation-Date: 2025-01-30 18:16+0100\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ko\n"
"Project-Id-Version: cf448e737e0d6d7b78742f963d761c61\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-01-01 00:00\n"
"Last-Translator: \n"
"Language-Team: Korean\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: cf448e737e0d6d7b78742f963d761c61\n"
"X-Crowdin-Project-ID: 1\n"
"X-Crowdin-Language: ko\n"
"X-Crowdin-File: /packages/twenty-front/src/locales/en.po\n"
"X-Crowdin-File-ID: 29\n"

#. js-lingui-id: /8h/VA
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid " must be one of {ratingValues} values"
msgstr ""

#. js-lingui-id: gdf0h7
#: src/modules/settings/admin-panel/components/SettingsAdminEnvVariables.tsx
#~ msgid ""
#~ " These are only the server values. Ensure your worker environment has the\n"
#~ "        same variables and values, this is required for asynchronous tasks like\n"
#~ "        email sync."
#~ msgstr ""
#~ " These are only the server values. Ensure your worker environment has the\n"
#~ "        same variables and values, this is required for asynchronous tasks like\n"
#~ "        email sync."

#. js-lingui-id: ypz2+E
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Empty"
msgstr ": 비어 있음"

#. js-lingui-id: CE75IR
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Future"
msgstr ": 미래"

#. js-lingui-id: Nk/d+Z
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Not"
msgstr ": 아닌지"

#. js-lingui-id: 1ORnec
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": NotEmpty"
msgstr ": 비어있지 않음"

#. js-lingui-id: 11QpPG
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": NotNull"
msgstr ": 널 아님"

#. js-lingui-id: gK4ysT
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Past"
msgstr ": 과거"

#. js-lingui-id: 4iJDkt
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid ": Today"
msgstr ": 오늘"

#. js-lingui-id: nSri0b
#: src/modules/views/components/ViewBarFilterDropdownVectorSearchButton.tsx
msgid "· {fieldSearchInputValue}"
msgstr ""

#. js-lingui-id: ZBGbuw
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
#: src/modules/object-record/record-field/meta-types/input/components/RawJsonFieldInput.tsx
msgid "[empty string]"
msgstr "[빈 문자열]"

#. js-lingui-id: J/hVSQ
#. placeholder {0}: theme.id
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownThemesComponents.tsx
msgid "{0}"
msgstr "{0}"

#. js-lingui-id: ROdDR9
#: src/modules/object-record/record-board/record-board-column/utils/computeAggregateValueAndLabel.ts
msgid "{aggregateLabel} of {fieldLabel}"
msgstr "{fieldLabel}의 {aggregateLabel}"

#. js-lingui-id: z5EvY5
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "{apiKeyName}"
msgstr ""

#. js-lingui-id: uogEAL
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#~ msgid "{apiKeyName} API Key"
#~ msgstr "{apiKeyName} API 키"

#. js-lingui-id: zi5SHH
#: src/modules/object-record/record-index/components/RecordIndexPageHeader.tsx
msgid "{contextStoreNumberOfSelectedRecords} selected"
msgstr "{contextStoreNumberOfSelectedRecords} 선택됨"

#. js-lingui-id: WS/avX
#: src/modules/spreadsheet-import/steps/components/ImportDataStep.tsx
msgid "{formattedCreatedRecordsProgress} out of {formattedRecordsToImportCount} records imported."
msgstr ""

#. js-lingui-id: wapGcj
#: src/modules/auth/components/VerifyEmailEffect.tsx
#~ msgid "{message}"
#~ msgstr ""

#. js-lingui-id: 6j5rE1
#: src/modules/action-menu/actions/record-agnostic-actions/run-workflow-actions/hooks/useRunWorkflowRecordAgnosticActions.tsx
#: src/modules/action-menu/actions/record-actions/run-workflow-actions/hooks/useRunWorkflowRecordActions.tsx
msgid "{name}"
msgstr "{name}"

#. js-lingui-id: OeIgEg
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelOverrideCell.tsx
msgid "{roleLabel} can {humanReadableAction} {objectLabel} records"
msgstr ""

#. js-lingui-id: D0C09b
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelOverrideCell.tsx
msgid "{roleLabel} can't {humanReadableAction} {objectLabel} records"
msgstr ""

#. js-lingui-id: pDgeaz
#: src/modules/error-handler/components/AppRootErrorFallback.tsx
msgid "{title}"
msgstr ""

#. js-lingui-id: WN9tFl
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignmentConfirmationModalSubtitle.tsx
msgid "{workspaceMemberName} will be unassigned from the following role:"
msgstr "{workspaceMemberName}님이 다음 역할에서 재할당 해제됩니다:"

#. js-lingui-id: CwstSL
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "••••••••"
msgstr ""

#. js-lingui-id: NNQszp
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
#~ msgid "⚠️ Please verify the auto mapping of the columns. You can also ignore or change the mapping of the columns."
#~ msgstr ""

#. js-lingui-id: YT0WJ4
#: src/pages/onboarding/ChooseYourPlan.tsx
#~ msgid "1 000 workflow node executions"
#~ msgstr "1 000 workflow node executions"

#. js-lingui-id: vb5TwV
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldSelect.tsx
msgid "1. Select a field type"
msgstr "1. 필드 유형 선택"

#. js-lingui-id: XSdCGq
#: src/pages/settings/roles/SettingsRoleAddObjectLevel.tsx
msgid "1. Select an object"
msgstr ""

#. js-lingui-id: SLjiTq
#: src/modules/settings/data-model/components/SettingsDataModelNewFieldBreadcrumbDropDown.tsx
#: src/modules/settings/data-model/components/SettingsDataModelNewFieldBreadcrumbDropDown.tsx
msgid "1. Type"
msgstr "1. 유형"

#. js-lingui-id: AvXug3
#: src/pages/onboarding/ChooseYourPlan.tsx
#~ msgid "10 000 workflow node executions"
#~ msgstr "워크플로 노드 실행 10,000회"

#. js-lingui-id: 7IIx+y
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "10,000 workflow node executions"
msgstr ""

#. js-lingui-id: 4EdXYs
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeFormatSelect.tsx
msgid "12h ({hour12Label})"
msgstr "12시간 ({hour12Label})"

#. js-lingui-id: yXvRMf
#: src/modules/settings/data-model/components/SettingsDataModelNewFieldBreadcrumbDropDown.tsx
#: src/modules/settings/data-model/components/SettingsDataModelNewFieldBreadcrumbDropDown.tsx
msgid "2. Configure"
msgstr "2. 구성"

#. js-lingui-id: 0HAF12
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "2. Configure field"
msgstr "2. 필드 구성"

#. js-lingui-id: 0Tx9MD
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectForm.tsx
msgid "2. Set {objectLabelPlural} permissions"
msgstr ""

#. js-lingui-id: kAtmAv
#: src/pages/onboarding/ChooseYourPlan.tsx
#~ msgid "20 000 workflow node executions"
#~ msgstr "워크플로 노드 실행 20,000회"

#. js-lingui-id: YfwnsU
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "20,000 workflow node executions"
msgstr ""

#. js-lingui-id: QsMprd
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeFormatSelect.tsx
msgid "24h ({hour24Label})"
msgstr "24시간 ({hour24Label})"

#. js-lingui-id: IbMa7Z
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "993"
msgstr ""

#. js-lingui-id: nMTB1f
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "A shared environment where you will be able to manage your customer relations with your team."
msgstr "팀과 함께 고객 관계를 관리할 수 있는 공유 환경입니다."

#. js-lingui-id: /cSHSG
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "A verification email has been sent to"
msgstr ""

#. js-lingui-id: JE9zuL
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Ability to edit all settings"
msgstr ""

#. js-lingui-id: 09tRFp
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsSection.tsx
#~ msgid "Ability to interact with each object"
#~ msgstr "각 객체와 상호작용할 수 있는 능력"

#. js-lingui-id: 4lzAkF
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelSection.tsx
#~ msgid "Ability to interact with specific objects"
#~ msgstr ""

#. js-lingui-id: nJHSWx
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
#~ msgid "Ability to interact with this specific object"
#~ msgstr ""

#. js-lingui-id: ssjjFt
#: src/modules/ui/input/components/ImageInput.tsx
msgid "Abort"
msgstr "중단"

#. js-lingui-id: uyJsf6
#: src/pages/settings/data-model/SettingsNewObject.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "About"
msgstr "정보"

#. js-lingui-id: NBHoKd
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "About my subscription"
msgstr ""

#. js-lingui-id: DhgC7B
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "About this user"
msgstr "이 사용자에 대해"

#. js-lingui-id: PtK3Kn
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "About this workspace"
msgstr "이 작업공간에 대해"

#. js-lingui-id: AeXO77
#: src/pages/settings/accounts/SettingsAccounts.tsx
#: src/pages/settings/accounts/SettingsAccounts.tsx
msgid "Account"
msgstr "계정"

#. js-lingui-id: nD0Y+a
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/modules/settings/profile/components/DeleteAccount.tsx
msgid "Account Deletion"
msgstr "계정 삭제"

#. js-lingui-id: bPwFdf
#: src/pages/settings/accounts/SettingsAccountsEmails.tsx
#: src/pages/settings/accounts/SettingsAccountsCalendars.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Accounts"
msgstr "계정"

#. js-lingui-id: GGcxJO
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "ACS Url copied to clipboard"
msgstr ""

#. js-lingui-id: bwRvnp
#: src/modules/workflow/workflow-steps/workflow-actions/utils/getActionHeaderTypeOrThrow.ts
msgid "Action"
msgstr "액션"

#. js-lingui-id: 7L01XJ
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsTableHeader.tsx
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsTableHeader.tsx
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevelTableHeader.tsx
msgid "Actions"
msgstr "작업"

#. js-lingui-id: 9MxZtA
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsSection.tsx
msgid "Actions users can perform on all objects"
msgstr ""

#. js-lingui-id: xnXGwA
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelSection.tsx
msgid "Actions users can perform on specific objects"
msgstr ""

#. js-lingui-id: N4ZcCd
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
msgid "Actions users can perform on this object"
msgstr ""

#. js-lingui-id: Ws0qQB
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsSection.tsx
#~ msgid "Actions you can perform on all objects"
#~ msgstr ""

#. js-lingui-id: FQBaXG
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
#: src/modules/settings/data-model/object-details/components/SettingsObjectFieldDisabledActionDropdown.tsx
#: src/modules/information-banner/components/billing/InformationBannerEndTrialPeriod.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Activate"
msgstr "활성화"

#. js-lingui-id: tu8A/k
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Activate Workflow"
msgstr "워크플로 활성화"

#. js-lingui-id: F6pfE9
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Active"
msgstr "활성"

#. js-lingui-id: Mue4oc
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "Active API keys created by you or your team."
msgstr "본인 또는 팀이 생성한 활성 API 키입니다."

#. js-lingui-id: m16xKo
#: src/modules/ui/layout/page/components/PageAddButton.tsx
#~ msgid "Add"
#~ msgstr "Add"

#. js-lingui-id: MPPZ54
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsListCard.tsx
msgid "Add account"
msgstr "계정 추가"

#. js-lingui-id: HD0x5p
#: src/modules/settings/security/components/approvedAccessDomains/SettingsApprovedAccessDomainsListCard.tsx
msgid "Add Approved Access Domain"
msgstr "승인된 액세스 도메인 추가"

#. js-lingui-id: DpV70M
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowEditActionFormBuilder.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectFields.tsx
msgid "Add Field"
msgstr "필드 추가"

#. js-lingui-id: vCSBPD
#: src/modules/views/components/ViewBarDetailsAddFilterButton.tsx
msgid "Add filter"
msgstr "필터 추가"

#. js-lingui-id: 7XzAKI
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowFormEmptyMessage.tsx
msgid "Add inputs to your form"
msgstr ""

#. js-lingui-id: Hkobke
#: src/modules/object-record/record-table/record-table-section/components/RecordTableRecordGroupSectionAddNew.tsx
msgid "Add new"
msgstr "새로 추가"

#. js-lingui-id: dEO3Zx
#: src/pages/settings/data-model/SettingsObjects.tsx
msgid "Add object"
msgstr "개체 추가"

#. js-lingui-id: mFtRj8
#: src/pages/settings/roles/SettingsRoleAddObjectLevel.tsx
msgid "Add object permission"
msgstr ""

#. js-lingui-id: Dl5lVI
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectForm.tsx
msgid "Add option"
msgstr "옵션 추가"

#. js-lingui-id: M067Bn
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Add or remove users"
msgstr ""

#. js-lingui-id: 7YdkgZ
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowOutputSchemaBuilder.tsx
msgid "Add Output Field"
msgstr ""

#. js-lingui-id: O8tK4v
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelSection.tsx
msgid "Add rule"
msgstr ""

#. js-lingui-id: sgXUv+
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersListCard.tsx
msgid "Add SSO Identity Provider"
msgstr "SSO ID 공급자 추가"

#. js-lingui-id: 5+ttxv
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistInput.tsx
msgid "Add to blocklist"
msgstr "차단 목록에 추가"

#. js-lingui-id: yVOmgE
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
msgid "Add to Favorite"
msgstr "즐겨찾기에 추가"

#. js-lingui-id: pBsoKL
#: src/modules/action-menu/mock/action-menu-actions.mock.tsx
#: src/modules/action-menu/mock/action-menu-actions.mock.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Add to favorites"
msgstr "즐겨찾기에 추가"

#. js-lingui-id: q9e2Bs
#: src/modules/views/view-picker/components/ViewPickerListContent.tsx
msgid "Add view"
msgstr "보기 추가"

#. js-lingui-id: m2qDV8
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateTitle.ts
msgid "Add your first {objectLabel}"
msgstr "첫 번째 {objectLabel} 추가"

#. js-lingui-id: vLO+NG
#: src/modules/ui/layout/show-page/components/ShowPageSummaryCard.tsx
msgid "Added {beautifiedCreatedAt}"
msgstr "{beautifiedCreatedAt} 추가"

#. js-lingui-id: jEHeq+
#: src/modules/settings/security/components/approvedAccessDomains/SettingsApprovedAccessDomainsListCard.tsx
msgid "Added {beautifyPastDateRelative}"
msgstr "{beautifyPastDateRelative} 추가"

#. js-lingui-id: 9iIYOy
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistTable.tsx
msgid "Added to blocklist"
msgstr "차단 목록에 추가됨"

#. js-lingui-id: Eis4ey
#: src/modules/settings/roles/components/SettingsRolesDefaultRole.tsx
msgid "Adjust the role-related settings"
msgstr "역할 관련 설정 조정"

#. js-lingui-id: g1in8j
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/pages/settings/admin-panel/SettingsAdminConfigVariableDetails.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Admin Panel"
msgstr "관리자 패널"

#. js-lingui-id: ae6Tab
#: src/modules/settings/roles/role-permissions/components/SettingsRolePermissions.tsx
#~ msgid "Admin settings and system tools"
#~ msgstr ""

#. js-lingui-id: sxkWRg
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerDatabaseEventForm.tsx
msgid "Advanced"
msgstr ""

#. js-lingui-id: tMFFwF
#: src/modules/views/components/ViewBarFilterDropdownAdvancedFilterButton.tsx
msgid "Advanced filter"
msgstr "고급 필터"

#. js-lingui-id: WsKDNF
#: src/modules/navigation/components/SettingsNavigationDrawer.tsx
msgid "Advanced:"
msgstr "고급:"

#. js-lingui-id: 1Cox/a
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Afrikaans"
msgstr "아프리칸스어"

#. js-lingui-id: MnmJG1
#: src/modules/workflow/workflow-steps/workflow-actions/utils/getActionHeaderTypeOrThrow.ts
msgid "AI Agent"
msgstr ""

#. js-lingui-id: kNfr85
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
msgid "AI Model"
msgstr ""

#. js-lingui-id: QwgquD
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowOutputSchemaBuilder.tsx
msgid "AI Response Schema"
msgstr ""

#. js-lingui-id: N40H+G
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "All"
msgstr "전체"

#. js-lingui-id: 3saA7W
#: src/modules/object-record/record-show/record-detail-section/components/RecordDetailRelationSection.tsx
msgid "All ({relationRecordsCount})"
msgstr "전체 ({relationRecordsCount})"

#. js-lingui-id: 9ljU00
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "All Actions"
#~ msgstr "모든 작업"

#. js-lingui-id: 7EZqN0
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "All emails and events linked to this account will be deleted"
msgstr "이 계정에 연결된 모든 이메일 및 이벤트가 삭제됩니다"

#. js-lingui-id: 3xugyj
#: src/modules/spreadsheet-import/components/MatchColumnSelectFieldSelectDropdownContent.tsx
msgid "All fields"
msgstr ""

#. js-lingui-id: 623MHa
#: src/modules/settings/data-model/fields/forms/components/text/constants/TextDataModelSelectOptions.ts
msgid "All lines"
msgstr "모든 줄"

#. js-lingui-id: zIk4M6
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsSection.tsx
msgid "All objects"
msgstr ""

#. js-lingui-id: aFE/OW
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "All Objects"
#~ msgstr "모든 개체"

#. js-lingui-id: Hm90t3
#: src/modules/settings/roles/components/SettingsRolesList.tsx
msgid "All roles"
msgstr "모든 역할"

#. js-lingui-id: F8bx9e
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelObjectPicker.tsx
#~ msgid "All the basic objects"
#~ msgstr ""

#. js-lingui-id: XuuWVF
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelObjectPicker.tsx
msgid "All the standard objects"
msgstr ""

#. js-lingui-id: MLmnB2
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "All workspaces this user is a member of"
msgstr "이 사용자가 속한 모든 작업 공간"

#. js-lingui-id: pPTmfc
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelObjectPicker.tsx
msgid "All your custom objects"
msgstr ""

#. js-lingui-id: GMx1K0
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Allow logins through Google's single sign-on functionality."
msgstr "Google의 SSO 기능을 통한 로그인 허용."

#. js-lingui-id: dea+zy
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Allow logins through Microsoft's single sign-on functionality."
msgstr "Microsoft의 SSO 기능을 통한 로그인 허용."

#. js-lingui-id: U6bnfj
#: src/modules/settings/workspace/components/ToggleImpersonate.tsx
msgid "Allow Support Team Access"
msgstr ""

#. js-lingui-id: wMg43c
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Allow the invitation of new users by sharing an invite link."
msgstr "초대 링크 공유를 통한 새 사용자 초대 허용."

#. js-lingui-id: vHeVg5
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Allow users to sign in with an email and password."
msgstr "이메일과 비밀번호로 로그인 허용."

#. js-lingui-id: LG4K0m
#: src/pages/auth/PasswordReset.tsx
#~ msgid "An error occurred while updating password"
#~ msgstr "비밀번호 업데이트 중 오류가 발생했습니다"

#. js-lingui-id: XyOToQ
#: src/utils/get-error-message-from-apollo-error.util.ts
#: src/utils/get-error-message-from-apollo-error.util.ts
#: src/modules/ui/feedback/snack-bar-manager/hooks/useSnackBar.ts
msgid "An error occurred."
msgstr ""

#. js-lingui-id: mJ6m4C
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "An optional description"
msgstr "선택적 설명"

#. js-lingui-id: lxentK
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "An unexpected error occurred"
msgstr "예상치 못한 오류가 발생했습니다"

#. js-lingui-id: HZFm5R
#: src/modules/auth/sign-in-up/components/FooterNote.tsx
msgid "and"
msgstr "및"

#. js-lingui-id: xJR+Wq
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Anyone with an email address at these domains is allowed to sign up for this workspace."
msgstr "이 도메인의 이메일 주소를 가진 사람은 이 작업 공간에 가입할 수 있습니다."

#. js-lingui-id: OZtEcz
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "API"
msgstr "API"

#. js-lingui-id: 0RqpZr
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "API & Webhooks"
msgstr "API 및 웹훅"

#. js-lingui-id: yRnk5W
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "API Key"
msgstr "API 키"

#. js-lingui-id: r+NRG6
#: src/modules/settings/developers/components/ApiKeyInput.tsx
msgid "API Key copied to clipboard"
msgstr "API 키가 클립보드에 복사되었습니다"

#. js-lingui-id: 5h8ooz
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "API keys"
msgstr "API 키"

#. js-lingui-id: vByqA1
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "API Keys & Webhooks"
msgstr ""

#. js-lingui-id: kAtj+q
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "API Name"
msgstr "API 이름"

#. js-lingui-id: lwCAhN
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "API Name (Plural)"
msgstr "API 이름(복수형)"

#. js-lingui-id: KclpRp
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "API Name (Singular)"
msgstr "API 이름 (단수)"

#. js-lingui-id: Z3Brb2
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectForm.tsx
msgid "API values"
msgstr "API 값"

#. js-lingui-id: JR6nY7
#: src/pages/settings/developers/playground/SettingsRestPlayground.tsx
#: src/pages/settings/developers/playground/SettingsGraphQLPlayground.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "APIs"
msgstr "API"

#. js-lingui-id: aAIQg2
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Appearance"
msgstr "외관"

#. js-lingui-id: UDlRcx
#: src/modules/settings/security/components/approvedAccessDomains/SettingsSecurityApprovedAccessDomainValidationEffect.tsx
msgid "Approved access domain validated"
msgstr ""

#. js-lingui-id: 9tggYj
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Approved Domains"
msgstr "승인된 도메인"

#. js-lingui-id: 1844JP
#: src/pages/settings/security/SettingsSecurity.tsx
#~ msgid "Approved Email Domain"
#~ msgstr "Approved Email Domain"

#. js-lingui-id: 8HV3WN
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Arabic"
msgstr "아랍어"

#. js-lingui-id: 3iX0kh
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Are you sure that you want to change your billing interval?"
#~ msgstr "청구 주기를 변경하시겠습니까?"

#. js-lingui-id: CWb74a
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Are you sure that you want to change your billing interval? You will be charged immediately for the full year."
#~ msgstr ""

#. js-lingui-id: 3SRf5B
#: src/pages/settings/roles/components/RoleAssignmentConfirmationModalSubtitle.tsx
#~ msgid "Are you sure you want to assign this role?"
#~ msgstr "Are you sure you want to assign this role?"

#. js-lingui-id: 8Y3Jbl
#: src/modules/action-menu/actions/record-actions/multiple-records/hooks/useDeleteMultipleRecordsAction.tsx
#~ msgid "Are you sure you want to delete these records? They can be recovered from the Command menu ({osControlSymbol} + K)."
#~ msgstr "Are you sure you want to delete these records? They can be recovered from the Command menu ({osControlSymbol} + K)."

#. js-lingui-id: 2BZAqa
#: src/modules/action-menu/actions/record-actions/multiple-records/components/DeleteMultipleRecordsAction.tsx
msgid "Are you sure you want to delete these records? They can be recovered from the Command menu."
msgstr "이 기록들을 삭제하시겠습니까? Command 메뉴에서 복구할 수 있습니다."

#. js-lingui-id: Se0vJw
#: src/modules/action-menu/actions/record-actions/single-record/hooks/useDeleteSingleRecordAction.tsx
#~ msgid "Are you sure you want to delete this record? It can be recovered from the Command menu ({osControlSymbol} + K)."
#~ msgstr "Are you sure you want to delete this record? It can be recovered from the Command menu ({osControlSymbol} + K)."

#. js-lingui-id: yb2hF4
#: src/modules/action-menu/actions/record-actions/single-record/components/DeleteSingleRecordAction.tsx
msgid "Are you sure you want to delete this record? It can be recovered from the Command menu."
msgstr "이 기록을 삭제하시겠습니까? Command 메뉴에서 복구할 수 있습니다."

#. js-lingui-id: rnCqBK
#: src/modules/spreadsheet-import/provider/components/SpreadsheetImport.tsx
msgid "Are you sure? Your current information will not be saved."
msgstr ""

#. js-lingui-id: nYD/Cq
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
msgid "Ascending"
msgstr "오름차순"

#. js-lingui-id: 9ch9Mz
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignmentConfirmationModal.tsx
msgid "Assign {workspaceMemberName}?"
msgstr "{workspaceMemberName}님에게 할당하시겠습니까?"

#. js-lingui-id: 449JVv
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
msgid "Assign Role"
msgstr ""

#. js-lingui-id: 17M/rH
#: src/modules/settings/roles/components/SettingsRolesList.tsx
msgid "Assign roles to manage each member’s access and permissions"
msgstr ""

#. js-lingui-id: rfYmIr
#: src/modules/settings/roles/components/SettingsRolesList.tsx
#~ msgid "Assign roles to specify each member's access permissions"
#~ msgstr "각 구성원의 접근 권한을 지정하려면 역할을 할당하세요"

#. js-lingui-id: 2y2quh
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignment.tsx
msgid "Assign to member"
msgstr "멤버에게 할당"

#. js-lingui-id: OItM/o
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignment.tsx
msgid "Assigned members"
msgstr "할당된 멤버"

#. js-lingui-id: lxQ+5m
#: src/modules/settings/roles/components/SettingsRolesTableHeader.tsx
msgid "Assigned to"
msgstr "할당됨"

#. js-lingui-id: 0dtKl9
#: src/modules/settings/roles/role/components/SettingsRole.tsx
msgid "Assignment"
msgstr "할당"

#. js-lingui-id: H8QGSx
#: src/modules/auth/sign-in-up/components/internal/SignInUpPasswordField.tsx
msgid "At least 8 characters long."
msgstr "최소 8자 이상."

#. js-lingui-id: Y7Dx6e
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "At least one authentication method must be enabled"
msgstr "하나 이상의 인증 방법이 활성화되어야 합니다"

#. js-lingui-id: P8fBlG
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Authentication"
msgstr "인증"

#. js-lingui-id: htuqBH
#: src/modules/auth/hooks/useVerifyLogin.ts
msgid "Authentication failed"
msgstr "인증 실패"

#. js-lingui-id: yIVrHZ
#: src/pages/auth/Authorize.tsx
msgid "Authorize"
msgstr "승인"

#. js-lingui-id: wTBNbL
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Authorized URI"
msgstr "승인된 URI"

#. js-lingui-id: Ovw0c6
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Authorized URL copied to clipboard"
msgstr "승인된 URL이 클립보드에 복사되었습니다"

#. js-lingui-id: 2zJkmL
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Auto-creation"
msgstr "자동 생성"

#. js-lingui-id: YRT7ZW
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Automatically create contacts for people you've participated in an event with."
msgstr "이벤트에 함께 참여한 사람들에 대한 연락처를 자동으로 생성합니다."

#. js-lingui-id: lgw3U4
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Automatically create contacts for people."
msgstr "사람들에 대한 연락처를 자동으로 생성합니다."

#. js-lingui-id: RpExX0
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Automatically create People records when receiving or sending emails"
msgstr "이메일을 받거나 보낼 때 사람 레코드를 자동으로 생성"

#. js-lingui-id: csDS2L
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerManualForm.tsx
msgid "Available"
msgstr ""

#. js-lingui-id: 3uQmjD
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Average"
msgstr "평균"

#. js-lingui-id: iH8pgl
#: src/pages/onboarding/BookCall.tsx
#: src/modules/spreadsheet-import/components/StepNavigationButton.tsx
msgid "Back"
msgstr ""

#. js-lingui-id: Dht9W3
#: src/pages/not-found/NotFound.tsx
msgid "Back to content"
msgstr "콘텐츠로 돌아가기"

#. js-lingui-id: ehOkF+
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelObjectPicker.tsx
#~ msgid "Basics"
#~ msgstr ""

#. js-lingui-id: R+w/Va
#: src/pages/settings/SettingsBilling.tsx
#: src/pages/settings/SettingsBilling.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Billing"
msgstr "청구"

#. js-lingui-id: nJGwRf
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Billing interval"
msgstr ""

#. js-lingui-id: K1172m
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistSection.tsx
msgid "Blocklist"
msgstr "차단 목록"

#. js-lingui-id: 2yl5lQ
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Book a Call"
msgstr "통화 예약"

#. js-lingui-id: d/yr35
#: src/pages/onboarding/BookCallDecision.tsx
msgid "Book onboarding"
msgstr ""

#. js-lingui-id: 2X0NCp
#: src/pages/onboarding/BookCallDecision.tsx
msgid "Book your onboarding"
msgstr ""

#. js-lingui-id: bOwPjT
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/constants/output-field-type-options.ts
msgid "Boolean"
msgstr ""

#. js-lingui-id: qDsMss
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowOutputSchemaBuilder.tsx
msgid "Brief explanation of this output field"
msgstr ""

#. js-lingui-id: 8Pfllj
#: src/modules/auth/sign-in-up/components/FooterNote.tsx
msgid "By using Twenty, you agree to the"
msgstr "Twenty를 사용함으로써 귀하는 다음에 동의합니다"

#. js-lingui-id: PmmvzS
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterValue.tsx
msgid "Calculate"
msgstr "계산"

#. js-lingui-id: AjVXBS
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Calendar"
msgstr "캘린더"

#. js-lingui-id: wRpDAv
#: src/modules/command-menu/hooks/useOpenCalendarEventInCommandMenu.ts
msgid "Calendar Event"
msgstr "캘린더 이벤트"

#. js-lingui-id: wLtx+m
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Calendar settings"
msgstr "캘린더 설정"

#. js-lingui-id: LbIh3Y
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
msgid "Calendar Sync"
msgstr "캘린더 동기화"

#. js-lingui-id: HlJKLT
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
#~ msgid "Calendar Sync Status"
#~ msgstr "Calendar Sync Status"

#. js-lingui-id: EUpfsd
#: src/pages/settings/accounts/SettingsAccountsCalendars.tsx
#: src/pages/settings/accounts/SettingsAccountsCalendars.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Calendars"
msgstr "캘린더"

#. js-lingui-id: msssZq
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Can't change API names for standard objects"
msgstr "표준 객체에 대한 API 이름을 변경할 수 없습니다."

#. js-lingui-id: dEgA5A
#: src/pages/auth/Authorize.tsx
#: src/modules/ui/layout/modal/components/ConfirmationModal.tsx
#: src/modules/ui/feedback/snack-bar-manager/components/SnackBar.tsx
#: src/modules/spreadsheet-import/steps/components/ImportDataStep.tsx
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
#: src/modules/spreadsheet-import/provider/components/SpreadsheetImport.tsx
#: src/modules/settings/components/SaveAndCancelButtons/CancelButton.tsx
msgid "Cancel"
msgstr "취소"

#. js-lingui-id: 0TllC8
#: src/pages/onboarding/ChooseYourPlan.tsx
#~ msgid "Cancel anytime"
#~ msgstr "언제든지 취소 가능"

#. js-lingui-id: rRK/Lf
#: src/pages/settings/SettingsBilling.tsx
msgid "Cancel Plan"
msgstr "요금제 취소"

#. js-lingui-id: N6gPiD
#: src/pages/settings/SettingsBilling.tsx
msgid "Cancel your subscription"
msgstr "구독 취소"

#. js-lingui-id: M1RLfx
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Catalan"
msgstr "카탈로니아어"

#. js-lingui-id: OfzMnb
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Change {to}"
#~ msgstr "{to}로 변경"

#. js-lingui-id: feCRNv
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "Change email"
msgstr ""

#. js-lingui-id: VhMDMg
#: src/pages/auth/PasswordReset.tsx
#: src/modules/settings/profile/components/ChangePassword.tsx
#: src/modules/settings/profile/components/ChangePassword.tsx
msgid "Change Password"
msgstr "비밀번호 변경"

#. js-lingui-id: wRtBJP
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Change Plan"
msgstr ""

#. js-lingui-id: EYSFEW
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Change subdomain?"
msgstr ""

#. js-lingui-id: C8G9Mq
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Change to Organization Plan?"
msgstr ""

#. js-lingui-id: b0h4XW
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Change to yearly"
#~ msgstr ""

#. js-lingui-id: N8mUQE
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Change to Yearly?"
msgstr ""

#. js-lingui-id: AwAGI4
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "Check your Emails"
msgstr ""

#. js-lingui-id: SxwQE8
#: src/modules/billing/hooks/useHandleCheckoutSession.ts
msgid "Checkout session error. Please retry or contact Twenty team"
msgstr ""

#. js-lingui-id: SviKkE
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Chinese — Simplified"
msgstr "중국어 - 간체"

#. js-lingui-id: dzb4Ep
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Chinese — Traditional"
msgstr "중국어 - 번체"

#. js-lingui-id: NFfUic
#: src/pages/auth/SignInUp.tsx
msgid "Choose a Workspace"
msgstr ""

#. js-lingui-id: JEFFOR
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Choose an object"
#~ msgstr "개체 선택"

#. js-lingui-id: Qz73jD
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
msgid "Choose between OIDC and SAML protocols"
msgstr "OIDC와 SAML 프로토콜 중 선택"

#. js-lingui-id: 2ZFG9X
#: src/modules/settings/data-model/fields/forms/currency/components/SettingsDataModelFieldCurrencyForm.tsx
msgid "Choose between Short and Full"
msgstr ""

#. js-lingui-id: YcrXB2
#: src/modules/settings/data-model/fields/forms/currency/components/SettingsDataModelFieldCurrencyForm.tsx
msgid "Choose the default currency that will apply"
msgstr "적용될 기본 통화 선택"

#. js-lingui-id: LHce7q
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
msgid "Choose the fields that will identify your records"
msgstr "레코드를 식별할 필드를 선택하세요"

#. js-lingui-id: hIJigY
#: src/modules/settings/data-model/fields/forms/date/utils/getDisplayFormatSelectDescription.tsx
msgid "Choose the format used to display date value"
msgstr ""

#. js-lingui-id: KT6rEB
#: src/modules/settings/accounts/components/SettingsNewAccountSection.tsx
msgid "Choose your provider"
msgstr "제공업체 선택"

#. js-lingui-id: 9qP96p
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Choose your Trial"
msgstr "평가판 선택"

#. js-lingui-id: xCJdfg
#: src/modules/ui/input/components/internal/date/components/InternalDatePicker.tsx
msgid "Clear"
msgstr "지우기"

#. js-lingui-id: wu0RfR
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "Clear the field or \"X\" to revert to environment/default value."
msgstr ""

#. js-lingui-id: HevGC0
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowFormEmptyMessage.tsx
msgid "Click on \"Add Field\" below to add the first input to your form. The form will pop up on the user's screen when the workflow is launched from a manual trigger. For other types of triggers, it will be displayed in the Workflow run record page."
msgstr ""

#. js-lingui-id: RFmRvm
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowOutputSchemaBuilder.tsx
msgid "Click on \"Add Output Field\" below to define the structure of your AI agent's response. These fields will be used to format and validate the AI's output when the workflow is executed, and can be referenced by subsequent workflow steps."
msgstr ""

#. js-lingui-id: b8wwse
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "Click on the checkmark to apply your changes."
msgstr ""

#. js-lingui-id: b9Y4up
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Client ID"
msgstr "클라이언트 ID"

#. js-lingui-id: Bdj4LI
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Client Secret"
msgstr "클라이언트 비밀"

#. js-lingui-id: XUe4cu
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Client Settings"
msgstr "클라이언트 설정"

#. js-lingui-id: yz7wBu
#: src/modules/ui/feedback/snack-bar-manager/components/SnackBar.tsx
msgid "Close"
msgstr "닫기"

#. js-lingui-id: qYsAlX
#: src/modules/ui/layout/page-header/components/PageHeaderToggleCommandMenuButton.tsx
msgid "Close command menu"
msgstr "명령 메뉴 닫기"

#. js-lingui-id: EWPtMO
#: src/modules/workflow/workflow-steps/workflow-actions/utils/getActionHeaderTypeOrThrow.ts
msgid "Code"
msgstr "코드"

#. js-lingui-id: H86f9p
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
#: src/modules/object-record/record-field/meta-types/input/components/RawJsonFieldInput.tsx
msgid "Collapse"
msgstr "접기"

#. js-lingui-id: Xose0w
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelsGeneral.tsx
msgid "Color code"
msgstr "색상 코드"

#. js-lingui-id: 41xV/k
#: src/modules/spreadsheet-import/utils/setColumn.ts
msgid "column data is not compatible with Multi-Select."
msgstr ""

#. js-lingui-id: 93hd3e
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
msgid "Columns not matched:"
msgstr ""

#. js-lingui-id: Icd+JO
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerManualForm.tsx
msgid "Command menu icon"
msgstr ""

#. js-lingui-id: NM9bMd
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Compact view"
msgstr "컴팩트 보기"

#. js-lingui-id: s2QZS6
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Companies"
msgstr ""

#. js-lingui-id: 0CuAor
#: src/modules/workflow/workflow-steps/workflow-actions/find-records-action/components/WorkflowEditActionFindRecords.tsx
msgid "Conditions"
msgstr ""

#. js-lingui-id: Vj43Y/
#: src/pages/settings/admin-panel/SettingsAdminConfigVariableDetails.tsx
#: src/modules/settings/admin-panel/config-variables/components/SettingsAdminConfigVariables.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminContent.tsx
msgid "Config Variables"
msgstr ""

#. js-lingui-id: +zUMwJ
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Configure an SSO connection"
msgstr "SSO 연결 구성"

#. js-lingui-id: QTNsSm
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Configure and customize your calendar preferences."
msgstr "캘린더 환경설정을 구성하고 사용자 지정하세요."

#. js-lingui-id: aGwm+D
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Configure how dates are displayed across the app"
msgstr "앱 전체에 날짜가 표시되는 방식 구성"

#. js-lingui-id: ghdb7+
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelsGeneral.tsx
msgid "Configure how we should display your events in your calendar"
msgstr "캘린더에 이벤트를 어떻게 표시할지 구성합니다"

#. js-lingui-id: Bh4GBD
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Configure your emails and calendar settings."
msgstr "이메일 및 캘린더 설정 구성."

#. js-lingui-id: W4D3v/
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "Configure your IMAP email account"
msgstr ""

#. js-lingui-id: 7VpPHA
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettingsDeleteRoleConfirmationModal.tsx
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignmentConfirmationModal.tsx
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Confirm"
msgstr "확인"

#. js-lingui-id: FbJ7Si
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettingsDeleteRoleConfirmationModalSubtitle.tsx
msgid "Confirm deletion of {roleName} role? This cannot be undone. All members will be reassigned to the default role."
msgstr ""

#. js-lingui-id: D8ATlr
#: src/modules/settings/accounts/components/SettingsNewAccountSection.tsx
msgid "Connect a new account to your workspace"
msgstr "새 계정을 워크스페이스에 연결"

#. js-lingui-id: Zgi9Fd
#: src/modules/settings/accounts/components/SettingsAccountsListEmptyStateCard.tsx
msgid "Connect with Google"
msgstr "Google과 연결"

#. js-lingui-id: VZvZWq
#: src/modules/settings/accounts/components/SettingsAccountsListEmptyStateCard.tsx
msgid "Connect with IMAP"
msgstr ""

#. js-lingui-id: IOfqM8
#: src/modules/settings/accounts/components/SettingsAccountsListEmptyStateCard.tsx
msgid "Connect with Microsoft"
msgstr "Microsoft와 연결"

#. js-lingui-id: 9TzudL
#: src/pages/settings/accounts/SettingsAccounts.tsx
msgid "Connected accounts"
msgstr "연결된 계정"

#. js-lingui-id: Y2y0mC
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Contact auto-creation"
msgstr "연락처 자동 생성"

#. js-lingui-id: /5mghO
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Contains"
msgstr "포함"

#. js-lingui-id: M73whl
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Context"
msgstr "컨텍스트"

#. js-lingui-id: xGVfLh
#: src/pages/onboarding/InviteTeam.tsx
#: src/pages/onboarding/CreateWorkspace.tsx
#: src/pages/onboarding/CreateProfile.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/modules/spreadsheet-import/steps/components/SelectHeaderStep/SelectHeaderStep.tsx
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
#: src/modules/spreadsheet-import/components/StepNavigationButton.tsx
#: src/modules/auth/sign-in-up/components/SignInUpGlobalScopeForm.tsx
#: src/modules/auth/sign-in-up/components/internal/SignInUpWithCredentials.tsx
msgid "Continue"
msgstr "계속"

#. js-lingui-id: RvVi9c
#: src/modules/auth/sign-in-up/components/internal/SignInUpWithCredentials.tsx
msgid "Continue with Email"
msgstr "이메일로 계속"

#. js-lingui-id: oZyG4C
#: src/modules/auth/sign-in-up/components/internal/SignInUpWithGoogle.tsx
msgid "Continue with Google"
msgstr "Google로 계속"

#. js-lingui-id: ztoybH
#: src/modules/auth/sign-in-up/components/internal/SignInUpWithMicrosoft.tsx
msgid "Continue with Microsoft"
msgstr "Microsoft로 계속"

#. js-lingui-id: FxVG/l
#: src/hooks/useCopyToClipboard.tsx
msgid "Copied to clipboard"
msgstr ""

#. js-lingui-id: u+VWhB
#: src/pages/settings/workspace/SettingsCustomDomainRecords.tsx
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerWebhookForm.tsx
#: src/modules/settings/admin-panel/config-variables/components/SettingsAdminConfigCopyableText.tsx
msgid "Copied to clipboard!"
msgstr "클립보드에 복사되었습니다!"

#. js-lingui-id: CcGOj+
#: src/modules/command-menu/components/CommandMenu.tsx
#~ msgid "Copilot"
#~ msgstr "Copilot"

#. js-lingui-id: he3ygx
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
#: src/modules/settings/developers/components/ApiKeyInput.tsx
msgid "Copy"
msgstr "복사"

#. js-lingui-id: 7eVkEH
#: src/pages/onboarding/InviteTeam.tsx
msgid "Copy invitation link"
msgstr "초대 링크 복사"

#. js-lingui-id: y1eoq1
#: src/modules/workspace/components/WorkspaceInviteLink.tsx
msgid "Copy link"
msgstr "링크 복사"

#. js-lingui-id: eZ5HO9
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
msgid "Copy link to view"
msgstr "보기 링크 복사"

#. js-lingui-id: Ej5euX
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Copy this key as it will not be visible again"
msgstr "이 키는 다시 표시되지 않으므로 복사하세요"

#. js-lingui-id: SGsgDO
#: src/modules/settings/playground/constants/SettingsPlaygroundFormSchemaSelectOptions.ts
msgid "Core"
msgstr "코어"

#. js-lingui-id: w5Stxm
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
#~ msgid "Correct the issues and fill the missing data."
#~ msgstr ""

#. js-lingui-id: I99Miw
#: src/modules/billing/components/SettingsBillingMonthlyCreditsSection.tsx
msgid "Cost"
msgstr ""

#. js-lingui-id: iX2opZ
#: src/modules/billing/components/SettingsBillingMonthlyCreditsSection.tsx
msgid "Cost per 1k Extra Credits"
msgstr ""

#. js-lingui-id: VVRHmR
#: src/modules/settings/security/components/approvedAccessDomains/SettingsSecurityApprovedAccessDomainRowDropdownMenu.tsx
msgid "Could not delete approved access domain"
msgstr ""

#. js-lingui-id: s8lFtq
#: src/hooks/useCopyToClipboard.tsx
msgid "Couldn't copy to clipboard"
msgstr ""

#. js-lingui-id: wBMjJ2
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterDropdownContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownMenuContent.tsx
msgid "Count"
msgstr "개수"

#. js-lingui-id: EkZfen
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count all"
msgstr "전체 개수"

#. js-lingui-id: vQJINq
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count empty"
msgstr "비어 있는 개수"

#. js-lingui-id: sLDQbp
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count false"
msgstr "카운트 거짓"

#. js-lingui-id: DzRsDJ
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count not empty"
msgstr "비어 있지 않은 개수"

#. js-lingui-id: ft6dHY
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count true"
msgstr "정확한 개수"

#. js-lingui-id: 9FZBbf
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Count unique values"
msgstr "고유 값 개수"

#. js-lingui-id: hYgDIe
#: src/modules/views/view-picker/components/ViewPickerCreateButton.tsx
msgid "Create"
msgstr "생성"

#. js-lingui-id: zNoOC2
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateSubTitle.ts
msgid "Create a workflow and return here to view its versions"
msgstr "워크플로를 생성하고 여기로 돌아와 버전을 확인하세요"

#. js-lingui-id: Hn5erC
#: src/modules/auth/sign-in-up/components/SignInUpGlobalScopeForm.tsx
msgid "Create a workspace"
msgstr ""

#. js-lingui-id: uXGLuq
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "Create API key"
msgstr "API 키 생성"

#. js-lingui-id: d0DCww
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Create new record"
msgstr "새 레코드 생성"

#. js-lingui-id: UDbEyl
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Create new workflow"
msgstr ""

#. js-lingui-id: gSyzEV
#: src/pages/onboarding/CreateProfile.tsx
msgid "Create profile"
msgstr "프로필 생성"

#. js-lingui-id: RoyYUE
#: src/modules/settings/roles/components/SettingsRolesList.tsx
msgid "Create Role"
msgstr "역할 생성"

#. js-lingui-id: 6MP9lc
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupFieldsContent.tsx
msgid "Create select field"
msgstr "선택 필드 생성"

#. js-lingui-id: jmetDx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Create Select..."
msgstr ""

#. js-lingui-id: zt6Erc
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
#: src/modules/views/components/UpdateViewButtonGroup.tsx
msgid "Create view"
msgstr "보기 생성"

#. js-lingui-id: dkAPxi
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
msgid "Create Webhook"
msgstr "Webhook 생성"

#. js-lingui-id: kdLGki
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Create Workspace"
msgstr "워크스페이스 만들기"

#. js-lingui-id: 9chYz/
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Create your workspace"
msgstr "워크스페이스 만들기"

#. js-lingui-id: d+F6q9
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Created"
#~ msgstr "생성됨"

#. js-lingui-id: fR16ki
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Creating your data model..."
msgstr ""

#. js-lingui-id: TlL0Pl
#: src/pages/onboarding/CreateWorkspace.tsx
#~ msgid "Creating your schema"
#~ msgstr ""

#. js-lingui-id: YO4SdK
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Creating your workspace"
msgstr ""

#. js-lingui-id: R3PLzn
#: src/modules/object-record/object-filter-dropdown/components/ObjectFilterDropdownCurrencySelect.tsx
msgid "currencies"
msgstr ""

#. js-lingui-id: 6YzXhC
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "Current value from server environment. Set a custom value to override."
msgstr ""

#. js-lingui-id: Hd06oy
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
msgid "Current version"
msgstr "현재 버전"

#. js-lingui-id: p76aoR
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
#~ msgid "Current version:"
#~ msgstr "Current version:"

#. js-lingui-id: 8Tg/JR
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelObjectPicker.tsx
#: src/modules/settings/data-model/fields/forms/date/utils/getDisplayFormatLabel.ts
msgid "Custom"
msgstr ""

#. js-lingui-id: XQ681Q
#: src/pages/settings/workspace/SettingsCustomDomain.tsx
msgid "Custom Domain"
msgstr "사용자 지정 도메인"

#. js-lingui-id: 1GTWIA
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Custom domain updated"
msgstr "사용자 정의 도메인이 업데이트되었습니다"

#. js-lingui-id: 8skTDV
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Custom objects"
msgstr "사용자 지정 개체"

#. js-lingui-id: qt+EaC
#: src/modules/settings/data-model/object-details/components/tabs/ObjectFields.tsx
msgid "Customise the fields available in the {objectLabelSingular} views."
msgstr "{objectLabelSingular} 보기에서 사용 가능한 필드를 사용자 지정하세요."

#. js-lingui-id: CMhr4u
#: src/pages/settings/SettingsWorkspace.tsx
msgid "Customize Domain"
msgstr "도메인 사용자 지정"

#. js-lingui-id: RP2we8
#: src/modules/object-record/record-table/record-table-header/components/RecordTableHeaderPlusButtonContent.tsx
msgid "Customize fields"
msgstr "필드 사용자 지정"

#. js-lingui-id: bCJa9l
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Customize your workspace security"
msgstr "워크스페이스 보안 사용자 지정"

#. js-lingui-id: w9VTXG
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Czech"
msgstr "체코어"

#. js-lingui-id: Zz6Cxn
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettings.tsx
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
#: src/modules/settings/profile/components/DeleteAccount.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
msgid "Danger zone"
msgstr "위험 구역"

#. js-lingui-id: Fo2vDn
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Danish"
msgstr "덴마크어"

#. js-lingui-id: pvnfJD
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Dark"
msgstr "다크"

#. js-lingui-id: TtG/MN
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Data deletion"
msgstr "데이터 삭제"

#. js-lingui-id: 5cNMFz
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Data model"
msgstr "데이터 모델"

#. js-lingui-id: 4BuYDo
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Data Model"
msgstr ""

#. js-lingui-id: r+cVRP
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Data type"
msgstr "데이터 유형"

#. js-lingui-id: GEMJqo
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "Database configuration is currently disabled."
msgstr ""

#. js-lingui-id: mYGY3B
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/constants/output-field-type-options.ts
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterDropdownContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownMenuContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownContent.tsx
msgid "Date"
msgstr "날짜"

#. js-lingui-id: Ud9zHv
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Date and time"
msgstr "날짜 및 시간"

#. js-lingui-id: Lhd0oQ
#: src/pages/settings/profile/appearance/components/DateTimeSettingsDateFormatSelect.tsx
msgid "Date format"
msgstr "날짜 형식"

#. js-lingui-id: 5y3O+A
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Deactivate"
msgstr "비활성화"

#. js-lingui-id: qk4i22
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Deactivate \"Synchronize Objects Labels and API Names\" to set a custom API name"
msgstr "\"개체 레이블 및 API 이름 동기화\"를 비활성화하여 사용자 지정 API 이름을 설정하세요"

#. js-lingui-id: T2YbXF
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
msgid "Deactivate object"
msgstr "객체 비활성화"

#. js-lingui-id: gexAq8
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
msgid "Deactivate this field"
msgstr "이 필드 비활성화"

#. js-lingui-id: 4tpC8V
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Deactivate Workflow"
msgstr "워크플로 비활성화"

#. js-lingui-id: yAT3be
#: src/modules/settings/data-model/fields/forms/components/text/constants/TextDataModelSelectOptions.ts
msgid "Deactivated"
msgstr "비활성화됨"

#. js-lingui-id: ovBPCi
#: src/modules/settings/data-model/fields/forms/date/utils/getDisplayFormatLabel.ts
msgid "Default"
msgstr ""

#. js-lingui-id: mC21D6
#: src/modules/settings/data-model/fields/forms/address/components/SettingsDataModelFieldAddressForm.tsx
msgid "Default Country"
msgstr "기본 국가"

#. js-lingui-id: 4zuPQL
#: src/modules/settings/data-model/fields/forms/phones/components/SettingsDataModelFieldPhonesForm.tsx
msgid "Default Country Code"
msgstr "기본 국가 코드"

#. js-lingui-id: TlEEts
#: src/modules/settings/admin-panel/config-variables/utils/useSourceContent.ts
msgid "Default value"
msgstr ""

#. js-lingui-id: aQ8swY
#: src/modules/settings/data-model/fields/forms/currency/components/SettingsDataModelFieldCurrencyForm.tsx
#: src/modules/settings/data-model/fields/forms/boolean/components/SettingsDataModelFieldBooleanForm.tsx
msgid "Default Value"
msgstr "기본값"

#. js-lingui-id: Y2ImVJ
#: src/pages/settings/data-model/SettingsNewObject.tsx
msgid "Define the name and description of your object"
msgstr "개체의 이름과 설명을 정의하세요"

#. js-lingui-id: Ntu10D
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Define user roles and access levels"
msgstr ""

#. js-lingui-id: bQkkFU
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Define what will be visible to other users in your workspace"
msgstr "워크스페이스에서 다른 사용자에게 표시될 내용을 정의합니다"

#. js-lingui-id: HNlEFZ
#: src/modules/settings/roles/role-permissions/object-level-permissions/utils/objectPermissionKeyToHumanReadableText.ts
msgid "delete"
msgstr ""

#. js-lingui-id: cnGeoo
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
#: src/modules/views/view-picker/components/ViewPickerCreateButton.tsx
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
#: src/modules/settings/data-model/object-details/components/SettingsObjectFieldDisabledActionDropdown.tsx
#: src/modules/action-menu/mock/action-menu-actions.mock.tsx
#: src/modules/action-menu/mock/action-menu-actions.mock.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Delete"
msgstr "삭제"

#. js-lingui-id: 2nRIIp
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
msgid "Delete {objectLabel}"
msgstr ""

#. js-lingui-id: ZDGm40
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/modules/settings/profile/components/DeleteAccount.tsx
#: src/modules/settings/profile/components/DeleteAccount.tsx
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Delete account"
msgstr "계정 삭제"

#. js-lingui-id: gAz0S5
#: src/modules/settings/profile/components/DeleteAccount.tsx
msgid "Delete account and all the associated data"
msgstr "계정 및 모든 관련 데이터 삭제"

#. js-lingui-id: hGfWDm
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Delete API key"
msgstr "API 키 삭제"

#. js-lingui-id: x2p4/4
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowEditActionFormBuilder.tsx
msgid "Delete field"
msgstr ""

#. js-lingui-id: 4dpwsE
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#~ msgid "Delete record"
#~ msgstr "레코드 삭제"

#. js-lingui-id: kf0A63
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Delete records"
msgstr "레코드 삭제"

#. js-lingui-id: 0ptEoK
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
#~ msgid "Delete Records on {objectLabel}"
#~ msgstr ""

#. js-lingui-id: mcXHCJ
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsSection.tsx
msgid "Delete Records on All Objects"
msgstr ""

#. js-lingui-id: efjzvW
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettings.tsx
msgid "Delete role"
msgstr ""

#. js-lingui-id: v41A6S
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettingsDeleteRoleConfirmationModal.tsx
msgid "Delete Role Permanently"
msgstr ""

#. js-lingui-id: T6S2Ns
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Delete this integration"
msgstr "이 통합 삭제"

#. js-lingui-id: c0vHQI
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettings.tsx
msgid "Delete this role and assign a new role to its members"
msgstr ""

#. js-lingui-id: KSOhjo
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Delete this webhook"
msgstr ""

#. js-lingui-id: aRG49z
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
msgid "Delete view"
msgstr "보기 삭제"

#. js-lingui-id: snMaH4
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Delete webhook"
msgstr "Webhook 삭제"

#. js-lingui-id: UA2IpC
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Delete workflow"
msgstr "워크플로 삭제"

#. js-lingui-id: ABwG9x
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Delete workflows"
msgstr "워크플로 삭제"

#. js-lingui-id: kYu0eF
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
msgid "Delete workspace"
msgstr "워크스페이스 삭제"

#. js-lingui-id: mk2Ygs
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
msgid "Delete your whole workspace"
msgstr "전체 워크스페이스 삭제"

#. js-lingui-id: vGjmyl
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Deleted"
#~ msgstr "삭제됨"

#. js-lingui-id: kcGoDz
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#~ msgid "Deleted {objectNamePlural}"
#~ msgstr "Deleted {objectNamePlural}"

#. js-lingui-id: WH/5rN
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Deleted records"
msgstr "삭제된 레코드"

#. js-lingui-id: /TC7qI
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#~ msgid "Deleted runs"
#~ msgstr "삭제된 실행"

#. js-lingui-id: Wj5mzm
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#~ msgid "Deleted versions"
#~ msgstr "삭제된 버전"

#. js-lingui-id: gw3Tlm
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#~ msgid "Deleted workflows"
#~ msgstr "삭제된 워크플로"

#. js-lingui-id: Cko536
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
msgid "Descending"
msgstr "내림차순"

#. js-lingui-id: g+5exC
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
msgid "Describe what you want the AI to do..."
msgstr ""

#. js-lingui-id: Nu4oKW
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowOutputSchemaBuilder.tsx
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsTableHeader.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Description"
msgstr "설명"

#. js-lingui-id: +ow7t4
#: src/modules/settings/roles/role-permissions/object-level-permissions/utils/objectPermissionKeyToHumanReadableText.ts
msgid "destroy"
msgstr ""

#. js-lingui-id: 2xxBws
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Destroy"
msgstr "파기"

#. js-lingui-id: vI0W/N
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
msgid "Destroy {objectLabel}"
msgstr ""

#. js-lingui-id: fq4Wxy
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
#~ msgid "Destroy Records on {objectLabel}"
#~ msgstr ""

#. js-lingui-id: L68qSB
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsSection.tsx
msgid "Destroy Records on All Objects"
msgstr ""

#. js-lingui-id: n+SX4g
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Developers"
msgstr "개발자"

#. js-lingui-id: zAg2B9
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Discard Draft"
msgstr "초안 삭제"

#. js-lingui-id: Xm/s+u
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelsGeneral.tsx
msgid "Display"
msgstr "표시"

#. js-lingui-id: 8Vg8H7
#: src/modules/settings/data-model/fields/forms/number/components/SettingsDataModelFieldNumberForm.tsx
msgid "Display as a plain number or a percentage"
msgstr "일반 숫자 또는 백분율로 표시"

#. js-lingui-id: i66xz9
#: src/modules/settings/data-model/fields/forms/date/components/SettingsDataModelFieldDateForm.tsx
#~ msgid "Display as relative date"
#~ msgstr "상대 날짜로 표시"

#. js-lingui-id: e/xgrw
#: src/modules/settings/data-model/fields/forms/date/components/SettingsDataModelFieldDateForm.tsx
msgid "Display Format"
msgstr ""

#. js-lingui-id: GoHpxA
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "Display text on multiple lines"
msgstr "여러 줄에 텍스트 표시"

#. js-lingui-id: ji7sqw
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/components/TemplateColumn.tsx
#: src/modules/spreadsheet-import/components/MatchColumnSelectFieldSelectDropdownContent.tsx
msgid "Do not import"
msgstr ""

#. js-lingui-id: U9q4zF
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Doesn't contain"
msgstr "포함하지 않음"

#. js-lingui-id: EoKe5U
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Domain"
msgstr "도메인"

#. js-lingui-id: yGpVSw
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
#~ msgid "Domain added successfully."
#~ msgstr "도메인이 성공적으로 추가되었습니다."

#. js-lingui-id: 7kVRe6
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Domains have to be smaller than 256 characters, cannot contain spaces and cannot contain any special characters."
msgstr "도메인은 256자보다 작아야 하며, 공백을 포함할 수 없고 특수 문자를 포함할 수 없습니다."

#. js-lingui-id: KUxVkp
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "Don’t auto-create contacts."
msgstr "연락처를 자동 생성하지 않음."

#. js-lingui-id: cx0Ws8
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Don’t create contacts from/to Gmail, Outlook emails"
msgstr "Gmail, Outlook 이메일에서/로 연락처를 생성하지 않음"

#. js-lingui-id: 3qDEYI
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Don’t sync emails from team@ support@ noreply@..."
msgstr "team@, support@, noreply@... 이메일과 동기화하지 않음..."

#. js-lingui-id: WcWS//
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Download file"
msgstr "파일 다운로드"

#. js-lingui-id: wrT4hq
#: src/modules/spreadsheet-import/steps/components/UploadStep/components/DropZone.tsx
msgid "Download sample file."
msgstr ""

#. js-lingui-id: 6D/BnN
#: src/modules/spreadsheet-import/steps/components/UploadStep/components/DropZone.tsx
msgid "Drop file here..."
msgstr ""

#. js-lingui-id: KIjvtr
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Dutch"
msgstr "네덜란드어"

#. js-lingui-id: QVVmxi
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "E.g. backoffice integration"
msgstr "예: 백오피스 통합"

#. js-lingui-id: GhTFTJ
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowOutputSchemaBuilder.tsx
msgid "e.g., summary, status, count"
msgstr ""

#. js-lingui-id: tOkc8o
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "Earliest"
msgstr "가장 빠름"

#. js-lingui-id: JTbQuO
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Earliest date"
msgstr "가장 빠른 날짜"

#. js-lingui-id: ePK91l
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
#: src/modules/settings/data-model/object-details/components/SettingsObjectFieldDisabledActionDropdown.tsx
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableActionButtons.tsx
#: src/modules/object-record/record-group/hooks/useRecordGroupActions.ts
msgid "Edit"
msgstr "편집"

#. js-lingui-id: fGxkii
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
msgid "Edit {objectLabel}"
msgstr ""

#. js-lingui-id: v+uKyy
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Edit billing interval"
#~ msgstr "청구 주기 편집"

#. js-lingui-id: 6vZ75d
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Edit CRM data structure and fields"
msgstr ""

#. js-lingui-id: 9QCQIc
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownHiddenRecordGroupsContent.tsx
msgid "Edit field values"
msgstr "필드 값 수정"

#. js-lingui-id: oKQ7ls
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownHiddenFieldsContent.tsx
msgid "Edit Fields"
msgstr "필드 수정"

#. js-lingui-id: 8gm+y4
#: src/modules/settings/accounts/components/SettingsAccountsEditImapConnection.tsx
#: src/modules/settings/accounts/components/SettingsAccountsEditImapConnection.tsx
msgid "Edit IMAP Connection"
msgstr ""

#. js-lingui-id: h2KoTu
#: src/pages/settings/SettingsBilling.tsx
msgid "Edit payment method, see your invoices and more"
msgstr "결제 방법 편집, 송장 확인 등"

#. js-lingui-id: MGkYcE
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
#~ msgid "Edit Records on {objectLabel}"
#~ msgstr ""

#. js-lingui-id: 6n2zpV
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsSection.tsx
msgid "Edit Records on All Objects"
msgstr ""

#. js-lingui-id: 6o1M/Q
#: src/pages/settings/SettingsWorkspace.tsx
msgid "Edit your subdomain name or set a custom domain."
msgstr "하위 도메인 이름을 편집하거나 사용자 지정 도메인을 설정하세요."

#. js-lingui-id: O3oNi5
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsProfile.tsx
#: src/pages/auth/PasswordReset.tsx
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignmentTableHeader.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Email"
msgstr "이메일"

#. js-lingui-id: hzKQCy
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "Email Address"
msgstr ""

#. js-lingui-id: 2SWjdJ
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
#~ msgid "Email can not be empty"
#~ msgstr "Email can not be empty"

#. js-lingui-id: unEEog
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Email cannot be empty"
msgstr "이메일은 비워 둘 수 없습니다"

#. js-lingui-id: Zb5c/r
#: src/modules/settings/accounts/components/SettingsAccountsNewImapConnection.tsx
#: src/modules/settings/accounts/components/SettingsAccountsEditImapConnection.tsx
msgid "Email Connections"
msgstr ""

#. js-lingui-id: lfQsvW
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Email integration"
msgstr "이메일 통합"

#. js-lingui-id: QT/Wo7
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistInput.tsx
msgid "Email or domain is already in blocklist"
msgstr "이메일 또는 도메인이 이미 차단 목록에 있습니다"

#. js-lingui-id: LimKOG
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Email verification"
msgstr "이메일 인증"

#. js-lingui-id: FiEQ3l
#: src/modules/auth/components/VerifyEmailEffect.tsx
msgid "Email verification failed"
msgstr ""

#. js-lingui-id: hn6Eox
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "Email Verification Failed"
msgstr ""

#. js-lingui-id: TBv/iZ
#: src/modules/auth/components/VerifyEmailEffect.tsx
#~ msgid "Email verification failed."
#~ msgstr "이메일 인증 실패."

#. js-lingui-id: svTijF
#: src/modules/auth/sign-in-up/hooks/useHandleResendEmailVerificationToken.ts
msgid "Email verification link resent!"
msgstr "이메일 인증 링크가 재발송되었습니다!"

#. js-lingui-id: 00icDW
#: src/modules/auth/components/VerifyEmailEffect.tsx
msgid "Email verified."
msgstr "이메일이 인증되었습니다."

#. js-lingui-id: VI2hiF
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistTable.tsx
msgid "Email/Domain"
msgstr "이메일/도메인"

#. js-lingui-id: BXEcos
#: src/pages/settings/accounts/SettingsAccountsEmails.tsx
#: src/pages/settings/accounts/SettingsAccountsEmails.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Emails"
msgstr "이메일"

#. js-lingui-id: Ww/M6X
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Emails settings"
msgstr "이메일 설정"

#. js-lingui-id: eXoH4Q
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "employees"
msgstr "직원"

#. js-lingui-id: gqv5ZL
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Employees"
msgstr "직원"

#. js-lingui-id: N2S1rs
#: src/modules/object-record/record-inline-cell/components/RecordInlineCellDisplayMode.tsx
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/activities/timeline-activities/rows/main-object/components/EventFieldDiff.tsx
msgid "Empty"
msgstr "비어 있음"

#. js-lingui-id: OMbipf
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
#: src/modules/object-record/record-field/meta-types/input/components/RawJsonFieldInput.tsx
msgid "Empty Array"
msgstr "빈 배열"

#. js-lingui-id: 3hSGoJ
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
#: src/modules/object-record/record-field/meta-types/input/components/RawJsonFieldInput.tsx
msgid "Empty Object"
msgstr "빈 객체"

#. js-lingui-id: q1srUM
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "Encryption"
msgstr ""

#. js-lingui-id: T3juzf
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Endpoint URL"
msgstr "엔드포인트 URL"

#. js-lingui-id: lYGfRP
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "English"
msgstr "영어"

#. js-lingui-id: /bfFKe
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Enjoy a {withCreditCardTrialPeriodDuration}-days free trial"
msgstr "{withCreditCardTrialPeriodDuration}일 무료 체험을 즐기세요"

#. js-lingui-id: oiVP/+
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableValueInput.tsx
msgid "Enter a value to store in database"
msgstr ""

#. js-lingui-id: 9/30HU
#: src/modules/settings/data-model/fields/forms/date/utils/getDisplayFormatSelectDescription.tsx
msgid "Enter in <0>Unicode</0> format"
msgstr ""

#. js-lingui-id: XJU8BD
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Enter the credentials to set the connection"
msgstr "연결 설정을 위해 자격 증명을 입력하세요"

#. js-lingui-id: rGWgcm
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Enter the infos to set the connection"
msgstr "연결 설정을 위한 정보를 입력하세요"

#. js-lingui-id: rYIISB
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Enter user ID or email address"
msgstr "사용자 ID 또는 이메일 주소 입력"

#. js-lingui-id: GpB8YV
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Enterprise"
msgstr "엔터프라이즈"

#. js-lingui-id: 8PrrNJ
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Entity ID copied to clipboard"
msgstr "개체 ID가 클립보드에 복사되었습니다"

#. js-lingui-id: 7RnAWe
#: src/modules/settings/admin-panel/components/SettingsAdminContent.tsx
#~ msgid "Env Variables"
#~ msgstr "Env 변수"

#. js-lingui-id: vPWtZ+
#: src/modules/settings/admin-panel/config-variables/utils/useSourceContent.ts
msgid "Environment variable"
msgstr ""

#. js-lingui-id: XOEl9R
#: src/modules/object-record/record-field/meta-types/display/components/PhonesFieldDisplay.tsx
msgid "Error copying to clipboard"
msgstr "클립보드에 복사 오류"

#. js-lingui-id: c3qGJX
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#~ msgid "Error deleting api key: {err}"
#~ msgstr "API 키 삭제 오류: {err}"

#. js-lingui-id: GHKxvg
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Error deleting api key."
msgstr ""

#. js-lingui-id: QnVLjD
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Error deleting invitation"
msgstr "초대 삭제 오류"

#. js-lingui-id: cyvTSq
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
msgid "Error deleting SSO Identity Provider"
msgstr "SSO ID 공급자 삭제 오류"

#. js-lingui-id: WEltn2
#: src/modules/settings/security/components/SSO/SettingsSecuritySSORowDropdownMenu.tsx
msgid "Error editing SSO Identity Provider"
msgstr "SSO ID 공급자 편집 오류"

#. js-lingui-id: 2GvilU
#: src/modules/activities/timeline-activities/rows/message/components/EventCardMessage.tsx
msgid "Error loading message"
msgstr ""

#. js-lingui-id: bj7nh3
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#~ msgid "Error regenerating api key: {err}"
#~ msgstr "API 키 재생성 오류: {err}"

#. js-lingui-id: PfAip2
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Error regenerating api key."
msgstr ""

#. js-lingui-id: clfpgU
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Error resending invitation"
msgstr "초대 재발송 오류"

#. js-lingui-id: nB84mG
#: src/modules/settings/security/components/approvedAccessDomains/SettingsSecurityApprovedAccessDomainValidationEffect.tsx
msgid "Error validating approved access domain"
msgstr ""

#. js-lingui-id: QmR+2U
#: src/modules/billing/hooks/useEndSubscriptionTrialPeriod.ts
msgid "Error while ending trial period. Please contact Twenty team."
msgstr ""

#. js-lingui-id: VSQxWH
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Error while switching subscription {to}."
#~ msgstr "구독 전환 중 오류 발생: {to}."

#. js-lingui-id: JKNROf
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Error while switching subscription to Organization Plan."
msgstr ""

#. js-lingui-id: k06M7e
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Error while switching subscription to yearly."
#~ msgstr ""

#. js-lingui-id: ENV7jU
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Error while switching subscription to Yearly."
msgstr ""

#. js-lingui-id: JLxMta
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
msgid "Establish Webhook endpoints for notifications on asynchronous events."
msgstr "비동기 이벤트 알림을 위한 Webhook 엔드포인트 설정."

#. js-lingui-id: poC90w
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelDetails.tsx
msgid "Event visibility"
msgstr "이벤트 가시성"

#. js-lingui-id: jvNRZW
#: src/modules/settings/accounts/components/SettingsAccountsCalendarChannelsGeneral.tsx
msgid "Events you participated in are displayed in red."
msgstr "참여한 이벤트가 빨간색으로 표시됩니다."

#. js-lingui-id: wqF3jl
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
#: src/modules/settings/accounts/components/SettingsAccountsCalendarVisibilitySettingsCard.tsx
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Everything"
msgstr "모두"

#. js-lingui-id: QQlMid
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Exclude group emails"
msgstr "그룹 이메일 제외"

#. js-lingui-id: +tk2yZ
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Exclude non-professional emails"
msgstr "비업무용 이메일 제외"

#. js-lingui-id: cIgBjB
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistSection.tsx
#~ msgid "Exclude the following people and domains from my email sync"
#~ msgstr "Exclude the following people and domains from my email sync"

#. js-lingui-id: yhhfqh
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistSection.tsx
msgid "Exclude the following people and domains from my email sync. Internal conversations will not be imported"
msgstr "다음 사람들과 도메인을 이메일 동기화에서 제외합니다. 내부 대화는 가져오지 않습니다"

#. js-lingui-id: fV7V51
#: src/pages/settings/data-model/SettingsObjects.tsx
msgid "Existing objects"
msgstr "기존 개체"

#. js-lingui-id: ydzS9x
#: src/modules/spreadsheet-import/provider/components/SpreadsheetImport.tsx
msgid "Exit"
msgstr ""

#. js-lingui-id: LFNXuj
#: src/modules/ui/layout/fullscreen/components/FullScreenContainer.tsx
#~ msgid "Exit Full Screen"
#~ msgstr "전체 화면 종료"

#. js-lingui-id: XB4onr
#: src/modules/spreadsheet-import/provider/components/SpreadsheetImport.tsx
msgid "Exit import flow"
msgstr ""

#. js-lingui-id: IZ4o2e
#: src/modules/navigation/components/SettingsNavigationDrawer.tsx
msgid "Exit Settings"
msgstr "설정 종료"

#. js-lingui-id: 1A3EXy
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepInputDetail.tsx
#: src/modules/settings/admin-panel/health-status/components/JsonDataIndicatorHealthStatus.tsx
#: src/modules/object-record/record-field/meta-types/input/components/RawJsonFieldInput.tsx
msgid "Expand"
msgstr "확장"

#. js-lingui-id: tXGQvS
#: src/modules/workflow/workflow-diagram/components/WorkflowDiagramCanvasEditableEffect.tsx
#~ msgid "Expected selected node to be a create step node."
#~ msgstr "Expected selected node to be a create step node."

#. js-lingui-id: bKBhgb
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Experience"
msgstr "경험"

#. js-lingui-id: LxRNPw
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/modules/settings/developers/components/SettingsApiKeysTable.tsx
msgid "Expiration"
msgstr "만료"

#. js-lingui-id: SkXfL0
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
msgid "Expiration Date"
msgstr "만료일"

#. js-lingui-id: M1RnFv
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Expired"
msgstr "만료됨"

#. js-lingui-id: i9qiyR
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Expires in"
msgstr "만료까지 남은 시간"

#. js-lingui-id: GS+Mus
#: src/modules/object-record/record-index/export/hooks/useRecordIndexExportRecords.ts
#: src/modules/action-menu/mock/action-menu-actions.mock.tsx
#: src/modules/action-menu/mock/action-menu-actions.mock.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Export"
msgstr "내보내기"

#. js-lingui-id: G5DJkP
#: src/modules/action-menu/actions/record-actions/constants/DefaultActionsConfig.ts
#~ msgid "Export record"
#~ msgstr "레코드 내보내기"

#. js-lingui-id: ep2rbf
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Export records"
msgstr "레코드 내보내기"

#. js-lingui-id: iHK6np
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
msgid "Export run"
msgstr "실행 내보내기"

#. js-lingui-id: vwtAUW
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
msgid "Export runs"
msgstr "실행 내보내기"

#. js-lingui-id: q46CjD
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Export to PDF"
msgstr "PDF로 내보내기"

#. js-lingui-id: BuprEs
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
msgid "Export version"
msgstr "버전 내보내기"

#. js-lingui-id: 4FLUle
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
msgid "Export versions"
msgstr "버전 내보내기"

#. js-lingui-id: DaGxE0
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Export view"
msgstr "보기 내보내기"

#. js-lingui-id: +FMXdE
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Export workflow"
msgstr "워크플로 내보내기"

#. js-lingui-id: XcAij/
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Export workflows"
msgstr "워크플로 내보내기"

#. js-lingui-id: eWCNmu
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.ts
#~ msgid "Export Workflows"
#~ msgstr "Export Workflows"

#. js-lingui-id: uE7S0L
#: src/modules/billing/components/SettingsBillingMonthlyCreditsSection.tsx
msgid "Extra Credits Used"
msgstr ""

#. js-lingui-id: bVax4d
#: src/modules/settings/admin-panel/config-variables/hooks/useConfigVariableActions.ts
#~ msgid "Failed to remove  override"
#~ msgstr ""

#. js-lingui-id: A/P7PX
#: src/modules/settings/admin-panel/config-variables/hooks/useConfigVariableActions.ts
msgid "Failed to remove override"
msgstr ""

#. js-lingui-id: Dzh4a2
#: src/modules/settings/admin-panel/config-variables/hooks/useConfigVariableActions.ts
msgid "Failed to update variable"
msgstr ""

#. js-lingui-id: jARNNi
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
#~ msgid "Failed to validate API key. Please check your API key and try again."
#~ msgstr "API 키를 검증하지 못했습니다. API 키를 확인하고 다시 시도하십시오."

#. js-lingui-id: ocUvR+
#: src/modules/settings/data-model/fields/forms/boolean/constants/BooleanDataModelSelectOptions.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "False"
msgstr "거짓"

#. js-lingui-id: X9kySA
#: src/modules/favorites/components/CurrentWorkspaceMemberFavoritesFolders.tsx
msgid "Favorites"
msgstr "즐겨찾기"

#. js-lingui-id: YXjpZx
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Feature Flag"
msgstr "기능 플래그"

#. js-lingui-id: kP/brT
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Feature Flags & Impersonation"
msgstr "기능 플래그 및 대행"

#. js-lingui-id: nrXDdR
#: src/modules/settings/data-model/fields/forms/relation/components/SettingsDataModelFieldRelationForm.tsx
msgid "Field name"
msgstr "필드 이름"

#. js-lingui-id: fV7qkH
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowOutputSchemaBuilder.tsx
msgid "Field Name"
msgstr ""

#. js-lingui-id: zXgopL
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Field type"
msgstr "필드 유형"

#. js-lingui-id: vF68cg
#: src/pages/settings/data-model/SettingsObjectIndexTable.tsx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
#: src/pages/settings/data-model/constants/SettingsObjectTableMetadata.ts
#: src/modules/settings/data-model/object-details/components/tabs/ObjectFields.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownFieldsContent.tsx
msgid "Fields"
msgstr "필드"

#. js-lingui-id: 3w/aqw
#: src/testing/mock-data/tableData.ts
msgid "Fields Count"
msgstr "필드 수"

#. js-lingui-id: o7J4JM
#: src/modules/views/components/ViewBarFilterButton.tsx
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
msgid "Filter"
msgstr "필터"

#. js-lingui-id: cSev+j
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Filters"
msgstr "필터"

#. js-lingui-id: JmZ/+d
#: src/pages/onboarding/InviteTeam.tsx
#: src/pages/onboarding/BookCallDecision.tsx
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectForm.tsx
msgid "Finish"
msgstr ""

#. js-lingui-id: SNdnlf
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
msgid "Finish flow with errors"
msgstr ""

#. js-lingui-id: USZ2N6
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Finnish"
msgstr "핀란드어"

#. js-lingui-id: ZyIk6Y
#: src/modules/settings/data-model/fields/forms/components/text/constants/TextDataModelSelectOptions.ts
msgid "First 10 lines"
msgstr "처음 10줄"

#. js-lingui-id: I3hko2
#: src/modules/settings/data-model/fields/forms/components/text/constants/TextDataModelSelectOptions.ts
msgid "First 2 lines"
msgstr "처음 2줄"

#. js-lingui-id: BDDkm3
#: src/modules/settings/data-model/fields/forms/components/text/constants/TextDataModelSelectOptions.ts
msgid "First 5 lines"
msgstr "처음 5줄"

#. js-lingui-id: kODvZJ
#: src/pages/onboarding/CreateProfile.tsx
#: src/modules/settings/profile/components/NameFields.tsx
msgid "First Name"
msgstr "이름"

#. js-lingui-id: 7JBW66
#: src/modules/object-record/record-field/meta-types/display/components/ForbiddenFieldDisplay.tsx
#~ msgid "Forbidden"
#~ msgstr ""

#. js-lingui-id: glx6on
#: src/modules/auth/sign-in-up/components/SignInUpWorkspaceScopeForm.tsx
msgid "Forgot your password?"
msgstr "비밀번호를 잊으셨나요?"

#. js-lingui-id: kI1qVD
#: src/modules/settings/data-model/fields/forms/currency/components/SettingsDataModelFieldCurrencyForm.tsx
msgid "Format"
msgstr ""

#. js-lingui-id: abDZwc
#: src/modules/settings/data-model/fields/forms/date/components/SettingsDataModelFieldDateForm.tsx
msgid "Format e.g. d-MMM-y (qqq''yy)"
msgstr ""

#. js-lingui-id: wjsFMQ
#: src/modules/billing/components/SettingsBillingMonthlyCreditsSection.tsx
msgid "Free Credits Used"
msgstr ""

#. js-lingui-id: nLC6tu
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "French"
msgstr "프랑스어"

#. js-lingui-id: aTieE0
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "from monthly to yearly"
#~ msgstr "월간에서 연간으로"

#. js-lingui-id: K04lE5
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "from yearly to monthly"
#~ msgstr "연간에서 월간으로"

#. js-lingui-id: scmRyR
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Full access"
msgstr "전체 접근"

#. js-lingui-id: xANKBj
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Functions"
msgstr "기능"

#. js-lingui-id: Weq9zb
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminContent.tsx
msgid "General"
msgstr "일반"

#. js-lingui-id: DDcvSo
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "German"
msgstr "독일어"

#. js-lingui-id: NXEW3h
#: src/pages/onboarding/InviteTeam.tsx
msgid "Get the most out of your workspace by inviting your team."
msgstr "팀을 초대하여 워크스페이스를 최대한 활용하세요."

#. js-lingui-id: zSGbaR
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Get your subscription"
msgstr "구독 신청"

#. js-lingui-id: 2GT3Hf
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Global"
msgstr "글로벌"

#. js-lingui-id: mUbv8L
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Go to Companies"
msgstr ""

#. js-lingui-id: dHvI9O
#: src/modules/workflow/components/OverrideWorkflowDraftConfirmationModal.tsx
msgid "Go to Draft"
msgstr ""

#. js-lingui-id: BQE4ob
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Go to Notes"
msgstr ""

#. js-lingui-id: buPcNi
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Go to Opportunities"
msgstr ""

#. js-lingui-id: MrE/Qb
#: src/modules/action-menu/mock/action-menu-actions.mock.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Go to People"
msgstr ""

#. js-lingui-id: iS69s6
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Go to runs"
msgstr "실행으로 이동"

#. js-lingui-id: mT57+Q
#: src/modules/views/view-picker/components/ViewPickerEditButton.tsx
#: src/modules/views/view-picker/components/ViewPickerCreateButton.tsx
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateRemote.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Go to Settings"
msgstr "설정으로 이동"

#. js-lingui-id: BM24SF
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Go to Tasks"
msgstr ""

#. js-lingui-id: A5WHZY
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Go to workflows"
msgstr "워크플로우로 이동"

#. js-lingui-id: 6eo66Q
#: src/modules/settings/workspace/components/ToggleImpersonate.tsx
msgid "Grant access to your workspace so we can troubleshoot problems."
msgstr ""

#. js-lingui-id: hWp1MY
#: src/pages/settings/SettingsWorkspace.tsx
#~ msgid "Grant Twenty support temporary access to your workspace so we can troubleshoot problems or recover content on your behalf. You can revoke access at any time."
#~ msgstr "Twenty 지원팀에 임시 접근 권한을 부여하여 문제를 해결하거나 콘텐츠를 복구할 수 있도록 하세요. 언제든지 접근 권한을 취소할 수 있습니다."

#. js-lingui-id: MM43i1
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsTableRow.tsx
msgid "Granted for {grantedBy} {pluralizedGrantedObject}"
msgstr ""

#. js-lingui-id: WUgtid
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevelTableRow.tsx
msgid "Granted for this object"
msgstr ""

#. js-lingui-id: gBiL6J
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "GraphQL"
msgstr "GraphQL"

#. js-lingui-id: /YalkJ
#: src/pages/settings/developers/playground/SettingsGraphQLPlayground.tsx
msgid "GraphQL API Playground"
msgstr "GraphQL API 플레이그라운드"

#. js-lingui-id: JXaffl
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
#~ msgid "Greater than"
#~ msgstr "보다 큼"

#. js-lingui-id: FJWI9L
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Greater than or equal"
msgstr ""

#. js-lingui-id: CZXzs4
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Greek"
msgstr "그리스어"

#. js-lingui-id: L8fEEm
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableOptionsDropdownContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Group"
msgstr ""

#. js-lingui-id: ALoP4W
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupsContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupFieldsContent.tsx
msgid "Group by"
msgstr "그룹별"

#. js-lingui-id: I1IOmb
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/modules/settings/admin-panel/health-status/components/SettingsAdminHealthStatus.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminContent.tsx
msgid "Health Status"
msgstr "건강 상태"

#. js-lingui-id: 3oTCgM
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Hebrew"
msgstr "히브리어"

#. js-lingui-id: D+zLDD
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownHiddenFieldsContent.tsx
msgid "Hidden"
msgstr "숨김"

#. js-lingui-id: em+S/U
#: src/modules/views/components/ViewBarFilterDropdownFieldSelectMenu.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/advanced-filter/components/AdvancedFilterFieldSelectMenu.tsx
msgid "Hidden fields"
msgstr ""

#. js-lingui-id: oK+1Wj
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownHiddenFieldsContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownFieldsContent.tsx
msgid "Hidden Fields"
msgstr "숨겨진 필드"

#. js-lingui-id: vLyv1R
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
#: src/modules/object-record/record-group/hooks/useRecordGroupActions.ts
msgid "Hide"
msgstr "숨기기"

#. js-lingui-id: OlbYor
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Hide deleted"
msgstr "삭제 숨기기"

#. js-lingui-id: FHhlye
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Hide deleted records"
msgstr "삭제된 레코드 숨기기"

#. js-lingui-id: 0dZtKR
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
msgid "Hide deleted runs"
msgstr "삭제된 실행 숨기기"

#. js-lingui-id: Jc6FrI
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#~ msgid "Hide deleted versions"
#~ msgstr "삭제된 버전 숨기기"

#. js-lingui-id: aOZAIB
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Hide deleted workflows"
msgstr "삭제된 워크플로 숨기기"

#. js-lingui-id: HS8BG/
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupsContent.tsx
msgid "Hide empty groups"
msgstr "빈 그룹 숨기기"

#. js-lingui-id: wgmt7A
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableOptionsDropdownContent.tsx
msgid "Hide hidden groups"
msgstr ""

#. js-lingui-id: B06Bgk
#: src/pages/onboarding/CreateProfile.tsx
msgid "How you'll be identified on the app."
msgstr "앱에서 사용자를 식별하는 방법입니다."

#. js-lingui-id: k7iMla
#: src/modules/settings/admin-panel/health-status/components/SettingsAdminHealthStatus.tsx
msgid "How your system is doing"
msgstr "시스템 상태"

#. js-lingui-id: DvpBQM
#: src/modules/workflow/workflow-steps/workflow-actions/utils/getActionHeaderTypeOrThrow.ts
msgid "HTTP Request"
msgstr ""

#. js-lingui-id: 0yRnXS
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "https://example.com/webhook"
msgstr ""

#. js-lingui-id: mkWad2
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Hungarian"
msgstr "헝가리어"

#. js-lingui-id: wwu18a
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Icon"
msgstr "아이콘"

#. js-lingui-id: XTWO+W
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "Icon and Name"
msgstr "아이콘 및 이름"

#. js-lingui-id: S0kLOH
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "ID"
msgstr "ID"

#. js-lingui-id: sJGljQ
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Identifier"
msgstr "식별자"

#. js-lingui-id: 06cbfQ
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Identity Provider"
msgstr "ID 공급자"

#. js-lingui-id: LPN8Ma
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Identity Provider Metadata XML"
msgstr "ID 공급자 메타데이터 XML"

#. js-lingui-id: ZfP/Ap
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateRemote.tsx
msgid "If this is unexpected, please verify your settings."
msgstr ""

#. js-lingui-id: j843N3
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "If you’ve lost this key, you can regenerate it, but be aware that any script using this key will need to be updated. Please type\"{confirmationValue}\" to confirm."
msgstr "이 키를 분실한 경우 다시 생성할 수 있지만, 이 키를 사용하는 모든 스크립트를 업데이트해야 합니다. 확인하려면 \"{confirmationValue}\"을 입력하세요."

#. js-lingui-id: 3rtE4o
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "IMAP Connection Details"
msgstr ""

#. js-lingui-id: jXXECN
#: src/modules/settings/accounts/hooks/useImapConnectionForm.ts
msgid "IMAP connection successfully created"
msgstr ""

#. js-lingui-id: Fz/wXI
#: src/modules/settings/accounts/hooks/useImapConnectionForm.ts
msgid "IMAP connection successfully updated"
msgstr ""

#. js-lingui-id: TE8s2c
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "IMAP Port"
msgstr ""

#. js-lingui-id: /RAwfY
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "IMAP Server"
msgstr ""

#. js-lingui-id: +8jOVa
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "imap.example.com"
msgstr ""

#. js-lingui-id: tSVr6t
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Impersonate"
msgstr "대행"

#. js-lingui-id: l3s5ri
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Import"
msgstr "가져오기"

#. js-lingui-id: eECp4f
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Import records"
msgstr "레코드 가져오기"

#. js-lingui-id: ZsIcZZ
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Import workflows"
msgstr "워크플로 가져오기"

#. js-lingui-id: RqSvT0
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsRowRightContainer.tsx
msgid "Importing"
msgstr "가져오는 중"

#. js-lingui-id: YgmSaz
#: src/modules/spreadsheet-import/steps/components/ImportDataStep.tsx
msgid "Importing Data ..."
msgstr ""

#. js-lingui-id: NoNwIX
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Inactive"
msgstr "비활성"

#. js-lingui-id: pZ/USH
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
msgid "Indexes"
msgstr "색인"

#. js-lingui-id: JE2tjr
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Input must be in camel case and cannot start with a number"
msgstr "입력은 카멜 케이스여야 하며 숫자로 시작할 수 없습니다"

#. js-lingui-id: /6i28D
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowEditActionFormFieldSettings.tsx
msgid "Input settings"
msgstr "입력 설정"

#. js-lingui-id: AwUsnG
#: src/pages/settings/data-model/constants/SettingsObjectTableMetadata.ts
msgid "Instances"
msgstr "인스턴스"

#. js-lingui-id: g+29+d
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
msgid "Instructions for AI"
msgstr ""

#. js-lingui-id: nbfdhU
#: src/pages/settings/integrations/SettingsIntegrations.tsx
#: src/pages/settings/integrations/SettingsIntegrations.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Integrations"
msgstr "통합"

#. js-lingui-id: GNRDhm
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "Invalid API key"
msgstr ""

#. js-lingui-id: NtFk/Z
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Invalid auth provider"
msgstr "잘못된 인증 제공자"

#. js-lingui-id: qcXnvu
#: src/pages/settings/workspace/SettingsDomain.tsx
#~ msgid "Invalid custom domain. Custom domains have to be smaller than 256 characters in length, cannot be IP addresses, cannot contain spaces, cannot contain any special characters such as _~`!@#$%^*()=+{}[]|\\;:'\",<>/? and cannot begin or end with a '-' character."
#~ msgstr "Invalid custom domain. Custom domains have to be smaller than 256 characters in length, cannot be IP addresses, cannot contain spaces, cannot contain any special characters such as _~`!@#$%^*()=+{}[]|\\;:'\",<>/? and cannot begin or end with a '-' character."

#. js-lingui-id: P3qQyo
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Invalid custom domain. Please include at least one subdomain (e.g., sub.example.com)."
msgstr "유효하지 않은 사용자 정의 도메인입니다. 적어도 하나의 하위 도메인을 포함해야 합니다 (예: sub.example.com)."

#. js-lingui-id: u3hwhx
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Invalid domain. Domains have to be smaller than 256 characters in length, cannot be IP addresses, cannot contain spaces, cannot contain any special characters such as _~`!@#$%^*()=+{}[]|\\;:'\",<>/? and cannot begin or end with a '-' character."
msgstr "잘못된 도메인입니다. 도메인은 길이가 256자를 넘을 수 없고, IP 주소가 될 수 없으며, 공백이 포함될 수 없고, _~`!@#$%^*()=+{}[]|\\;:'\",<>/?와 같은 특수 문자를 포함할 수 없으며, '-' 문자로 시작하거나 끝나선 안 됩니다."

#. js-lingui-id: B2Tpo0
#: src/modules/auth/sign-in-up/hooks/useHandleResetPassword.ts
#: src/modules/auth/sign-in-up/hooks/useHandleResendEmailVerificationToken.ts
msgid "Invalid email"
msgstr "잘못된 이메일"

#. js-lingui-id: /m52AE
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistInput.tsx
#: src/modules/settings/accounts/components/SettingsAccountsBlocklistInput.tsx
msgid "Invalid email or domain"
msgstr "잘못된 이메일 또는 도메인"

#. js-lingui-id: b2B7Ze
#: src/modules/auth/components/VerifyEmailEffect.tsx
msgid "Invalid email verification link."
msgstr "잘못된 이메일 인증 링크입니다."

#. js-lingui-id: uzxr9u
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Invalid File"
msgstr "잘못된 파일"

#. js-lingui-id: QdoUFL
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Invalid form values"
msgstr "잘못된 양식 값"

#. js-lingui-id: K8XJhc
#: src/modules/auth/sign-in-up/hooks/useHandleResetPassword.ts
msgid "Invalid workspace"
msgstr "잘못된 작업 공간"

#. js-lingui-id: ZR1dJ4
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/components/WorkspacesForSignUp.tsx
msgid "Invitations"
msgstr ""

#. js-lingui-id: MFKlMB
#: src/modules/workspace/components/WorkspaceInviteTeam.tsx
msgid "Invite"
msgstr "초대"

#. js-lingui-id: 0M8+El
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Invite by email"
msgstr "이메일로 초대"

#. js-lingui-id: PWIq/W
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Invite by link"
msgstr "링크로 초대"

#. js-lingui-id: 3athPG
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "Invite by Link"
msgstr "링크로 초대"

#. js-lingui-id: 5IfmKA
#: src/pages/onboarding/InviteTeam.tsx
msgid "Invite link sent to email addresses"
msgstr "이메일 주소로 전송된 초대 링크"

#. js-lingui-id: x1m5RZ
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Invite user"
msgstr "사용자 초대"

#. js-lingui-id: d+Y+rP
#: src/pages/onboarding/InviteTeam.tsx
msgid "Invite your team"
msgstr "팀 초대"

#. js-lingui-id: W153SA
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is"
msgstr "임"

#. js-lingui-id: N73SBG
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is after"
msgstr "다음 이후입니다"

#. js-lingui-id: NgIlDJ
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is before"
msgstr "다음 이전입니다"

#. js-lingui-id: Hte7bc
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is empty"
msgstr "비어 있습니다"

#. js-lingui-id: glSyvW
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is in future"
msgstr "미래에 있음"

#. js-lingui-id: F9Vw4E
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is in past"
msgstr "과거에 있음"

#. js-lingui-id: 5pz6vU
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is not"
msgstr "아님"

#. js-lingui-id: /2on+O
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid array"
msgstr ""

#. js-lingui-id: gNfGMx
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid calling code"
msgstr ""

#. js-lingui-id: Ka30cp
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid country code"
msgstr ""

#. js-lingui-id: LmaaLR
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid date (format: '2021-12-01')"
msgstr ""

#. js-lingui-id: ntHJJY
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid date time (format: '2021-12-01T00:00:00Z')"
msgstr ""

#. js-lingui-id: M9i8dv
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid email"
msgstr ""

#. js-lingui-id: 96g038
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid JSON"
msgstr ""

#. js-lingui-id: l5P+Rf
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid phone number"
msgstr ""

#. js-lingui-id: Ir+3AW
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid URL"
msgstr ""

#. js-lingui-id: MgIflq
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "is not a valid UUID"
msgstr ""

#. js-lingui-id: ldI7NO
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is not empty"
msgstr "비어 있지 않음"

#. js-lingui-id: HQFVAU
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is not null"
msgstr "널이 아님"

#. js-lingui-id: jPtV7x
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is relative"
msgstr "상대적임"

#. js-lingui-id: 0TLhix
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Is today"
msgstr "오늘임"

#. js-lingui-id: IhCN5p
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Issuer URI"
msgstr "발행자 URI"

#. js-lingui-id: Lj7sBL
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Italian"
msgstr "이탈리아어"

#. js-lingui-id: dFtidv
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Japanese"
msgstr "일본어"

#. js-lingui-id: wrLOeW
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "<EMAIL>"
msgstr ""

#. js-lingui-id: VIK/N0
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerWebhookForm.tsx
#~ msgid "JSON keys cannot contain spaces"
#~ msgstr ""

#. js-lingui-id: OGXtL8
#: src/modules/views/view-picker/constants/ViewPickerTypeSelectOptions.ts
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Kanban"
msgstr "칸반"

#. js-lingui-id: h6S9Yz
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Korean"
msgstr "한국어"

#. js-lingui-id: zrpwCd
#: src/pages/settings/lab/SettingsLab.tsx
#: src/pages/settings/lab/SettingsLab.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Lab"
msgstr "실험실"

#. js-lingui-id: vXIe7J
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Language"
msgstr "언어"

#. js-lingui-id: DU27lO
#: src/modules/settings/admin-panel/health-status/constants/WorkerQueueMetricsSelectOptions.ts
msgid "Last 1 hour"
msgstr "지난 <b>1</b>시간"

#. js-lingui-id: 0LzSIl
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 1 Hour (oldest → newest)"
msgstr "지난 1시간 (오래된 순 → 최신 순)"

#. js-lingui-id: XUlP/Y
#: src/modules/settings/admin-panel/health-status/constants/WorkerQueueMetricsSelectOptions.ts
msgid "Last 12 hours"
msgstr "지난 <b>12</b>시간"

#. js-lingui-id: n7EOpf
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 12 Hours (oldest → newest)"
msgstr "지난 12시간 (오래된 순 → 최신 순)"

#. js-lingui-id: j5nqnO
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 24 Hours (oldest → newest)"
msgstr "지난 24시간 (오래된 순 → 최신 순)"

#. js-lingui-id: gdAJ4I
#: src/modules/settings/admin-panel/health-status/constants/WorkerQueueMetricsSelectOptions.ts
msgid "Last 4 hours"
msgstr "지난 <b>4</b>시간"

#. js-lingui-id: MjAVB0
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 4 Hours (oldest → newest)"
msgstr "지난 4시간 (오래된 순 → 최신 순)"

#. js-lingui-id: GQgBSj
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Last 7 Days (oldest → newest)"
msgstr "지난 7일 (오래된 순 → 최신 순)"

#. js-lingui-id: UXBCwc
#: src/pages/onboarding/CreateProfile.tsx
#: src/modules/settings/profile/components/NameFields.tsx
msgid "Last Name"
msgstr "성"

#. js-lingui-id: s0kiVA
#: src/modules/information-banner/components/billing/InformationBannerFailPaymentInfo.tsx
msgid "Last payment failed. Please contact your admin."
msgstr ""

#. js-lingui-id: 3u/3zO
#: src/modules/information-banner/components/billing/InformationBannerFailPaymentInfo.tsx
msgid "Last payment failed. Please update your billing details."
msgstr ""

#. js-lingui-id: wL3cK8
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "Latest"
msgstr "최신"

#. js-lingui-id: Kcjbmz
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Latest date"
msgstr "최신 날짜"

#. js-lingui-id: zUzYWu
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
msgid "Latest version"
msgstr "최신 버전"

#. js-lingui-id: 7dWjtB
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
#~ msgid "Latest version:"
#~ msgstr "Latest version:"

#. js-lingui-id: ZEP8tT
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "Launch"
msgstr "시작"

#. js-lingui-id: rdU729
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Layout"
msgstr "레이아웃"

#. js-lingui-id: haJ2Hb
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
#~ msgid "Less than"
#~ msgstr "보다 작음"

#. js-lingui-id: 8PPI2g
#: src/modules/object-record/object-filter-dropdown/utils/getOperandLabel.ts
msgid "Less than or equal"
msgstr ""

#. js-lingui-id: 1njn7W
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Light"
msgstr "라이트"

#. js-lingui-id: pQjjYo
#: src/pages/onboarding/InviteTeam.tsx
#: src/modules/workspace/components/WorkspaceInviteLink.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
msgid "Link copied to clipboard"
msgstr "링크가 클립보드에 복사되었습니다"

#. js-lingui-id: DL2sg0
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Listings"
msgstr "목록"

#. js-lingui-id: Nx262D
#: src/modules/activities/files/components/DocumentViewer.tsx
msgid "Loading csv ... "
msgstr ""

#. js-lingui-id: s1MstR
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Loading metrics data..."
msgstr "메트릭스 데이터 로딩 중..."

#. js-lingui-id: Z3FXyt
#: src/modules/settings/admin-panel/components/SettingsAdminVersionDisplay.tsx
#: src/modules/activities/timeline-activities/rows/message/components/EventCardMessage.tsx
msgid "Loading..."
msgstr ""

#. js-lingui-id: FgAxTj
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
#: src/modules/auth/sign-in-up/components/SignInUpGlobalScopeForm.tsx
msgid "Log out"
msgstr "로그아웃"

#. js-lingui-id: nOhz3x
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Logout"
msgstr "로그아웃"

#. js-lingui-id: PTozs8
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Look up users and manage their workspace feature flags or impersonate them."
msgstr "사용자를 조회하여 그들의 워크스페이스 기능 플래그를 관리하거나 대행합니다."

#. js-lingui-id: GPSwzy
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Look up users to impersonate them."
msgstr "사용자를 조회하여 그들을 대행합니다."

#. js-lingui-id: n0FHLv
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Manage API keys and webhooks"
msgstr ""

#. js-lingui-id: nvgUPq
#: src/pages/settings/SettingsBilling.tsx
msgid "Manage billing information"
msgstr ""

#. js-lingui-id: U8dG4j
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
#: src/modules/views/view-picker/components/ViewPickerOptionDropdown.tsx
msgid "Manage favorite"
msgstr "즐겨찾기 관리"

#. js-lingui-id: T6YjCk
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Manage Members"
msgstr "회원 관리"

#. js-lingui-id: eGGH1l
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Manage security policies"
msgstr ""

#. js-lingui-id: 49MfD1
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Manage support access settings"
msgstr ""

#. js-lingui-id: 4cjU2u
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#~ msgid "Manage the members of your space here"
#~ msgstr "여기에서 워크스페이스의 회원을 관리하세요"

#. js-lingui-id: N7fMy9
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Manage the members of your workspace here"
msgstr ""

#. js-lingui-id: fEqDWx
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Manage workflows"
msgstr ""

#. js-lingui-id: FyFNsd
#: src/pages/settings/accounts/SettingsAccounts.tsx
msgid "Manage your internet accounts."
msgstr "인터넷 계정을 관리하세요."

#. js-lingui-id: 36kYu0
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Manage your subscription"
#~ msgstr "구독 관리"

#. js-lingui-id: 3Sdni6
#: src/modules/action-menu/components/__stories__/RecordIndexActionMenuDropdown.stories.tsx
#: src/modules/action-menu/components/__stories__/RecordIndexActionMenuBarEntry.stories.tsx
#~ msgid "Mark as done"
#~ msgstr "완료로 표시"

#. js-lingui-id: 7h8ch8
#: src/modules/spreadsheet-import/steps/components/SpreadsheetImportStepperContainer.tsx
#~ msgid "Match columns"
#~ msgstr ""

#. js-lingui-id: gDLior
#: src/modules/spreadsheet-import/steps/components/SpreadsheetImportStepperContainer.tsx
msgid "Match Columns"
msgstr ""

#. js-lingui-id: CK1KXz
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Max"
msgstr "최대"

#. js-lingui-id: HFjNu6
#: src/modules/spreadsheet-import/steps/components/UploadStep/components/DropZone.tsx
msgid "Max import capacity: {formatSpreadsheetMaxRecordImportCapacity} records. Otherwise, consider splitting your file or using the API."
msgstr ""

#. js-lingui-id: JGZq0R
#: src/modules/spreadsheet-import/steps/components/UploadStep/components/DropZone.tsx
#~ msgid "Max import capacity: {SpreadsheetMaxRecordImportCapacity} records. Otherwise, consider splitting your file or using the API."
#~ msgstr ""

#. js-lingui-id: Sma10A
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/components/WorkspacesForSignIn.tsx
msgid "Member of"
msgstr ""

#. js-lingui-id: wlQNTg
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Members"
msgstr "회원"

#. js-lingui-id: U15XwX
#: src/modules/activities/timeline-activities/rows/message/components/EventCardMessage.tsx
msgid "Message not found"
msgstr ""

#. js-lingui-id: elpx6r
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
msgid "Message Sync"
msgstr "메시지 동기화"

#. js-lingui-id: Lt5rXo
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
#~ msgid "Message Sync Status"
#~ msgstr "Message Sync Status"

#. js-lingui-id: 6GBt0m
#: src/modules/settings/playground/constants/SettingsPlaygroundFormSchemaSelectOptions.ts
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
#: src/modules/settings/accounts/components/SettingsAccountsCalendarVisibilitySettingsCard.tsx
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Metadata"
msgstr "메타데이터"

#. js-lingui-id: dN5YOb
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Metadata file generation failed"
msgstr "메타데이터 파일 생성 실패"

#. js-lingui-id: eTUF28
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Min"
msgstr "최소"

#. js-lingui-id: 8g3Cgz
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
msgid "Monitor the execution of your calendar events sync job"
msgstr "캘린더 이벤트 동기화 작업 실행 모니터링"

#. js-lingui-id: IL/nIb
#: src/modules/settings/admin-panel/health-status/components/ConnectedAccountHealthStatus.tsx
msgid "Monitor the execution of your emails sync job"
msgstr "이메일 동기화 작업 실행 모니터링"

#. js-lingui-id: +8Nek/
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Monthly"
msgstr ""

#. js-lingui-id: 1cOZuL
#: src/modules/billing/components/SettingsBillingMonthlyCreditsSection.tsx
msgid "Monthly Credits"
msgstr ""

#. js-lingui-id: 2FYpfJ
#: src/modules/ui/layout/tab-list/components/TabMoreButton.tsx
msgid "More"
msgstr ""

#. js-lingui-id: 3Siwmw
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterDropdownContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownMenuContent.tsx
msgid "More options"
msgstr "추가 옵션"

#. js-lingui-id: iSLA/r
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
#: src/modules/object-record/record-group/hooks/useRecordGroupActions.ts
msgid "Move left"
msgstr "왼쪽으로 이동"

#. js-lingui-id: Ubl2by
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
#: src/modules/object-record/record-group/hooks/useRecordGroupActions.ts
msgid "Move right"
msgstr "오른쪽으로 이동"

#. js-lingui-id: asWVA5
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "must be a number"
msgstr ""

#. js-lingui-id: qJVW/e
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "must be an array of object with valid phone, calling code and country code (format: '[{\"number\":\"123456789\", \"callingCode\":\"+33\", \"countryCode\":\"FR\"}]')"
msgstr ""

#. js-lingui-id: hTW2Fw
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "must be an array of object with valid url and label (format: '[{\"url\":\"valid.url\", \"label\":\"label value\")}]'"
msgstr ""

#. js-lingui-id: 5rovyq
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
msgid "must be an array of valid emails"
msgstr ""

#. js-lingui-id: amJfUq
#: src/modules/object-record/spreadsheet-import/utils/getSpreadSheetFieldValidationDefinitions.ts
#~ msgid "must contain only numbers"
#~ msgstr ""

#. js-lingui-id: 6YtxFj
#: src/testing/mock-data/tableData.ts
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/SettingsProfile.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
#: src/pages/settings/data-model/constants/SettingsObjectTableMetadata.ts
#: src/pages/onboarding/CreateProfile.tsx
#: src/modules/settings/workspace/components/NameField.tsx
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsTableHeader.tsx
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsTableHeader.tsx
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevelTableHeader.tsx
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignmentTableHeader.tsx
#: src/modules/settings/roles/components/SettingsRolesTableHeader.tsx
#: src/modules/settings/developers/components/SettingsApiKeysTable.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Name"
msgstr "이름"

#. js-lingui-id: XSwyCU
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Name can not be empty"
msgstr "이름은 비워 둘 수 없습니다"

#. js-lingui-id: zaxmAs
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
msgid "Name in both singular (e.g., 'Invoice') and plural (e.g., 'Invoices') forms."
msgstr "단수형 (예: 'Invoice')과 복수형 (예: 'Invoices')으로 이름을 지정하세요."

#. js-lingui-id: z+6jaZ
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Name of your API key"
msgstr "API 키 이름"

#. js-lingui-id: J7w8lI
#: src/pages/settings/SettingsWorkspace.tsx
msgid "Name of your workspace"
msgstr "워크스페이스 이름"

#. js-lingui-id: 2T8KCk
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Navigate to next record"
msgstr "다음 레코드로 이동"

#. js-lingui-id: UX6+vb
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#~ msgid "Navigate to next run"
#~ msgstr "다음 실행으로 이동"

#. js-lingui-id: veSA19
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
msgid "Navigate to next version"
msgstr "다음 버전으로 이동"

#. js-lingui-id: ZTEho+
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Navigate to next workflow"
msgstr "다음 워크플로로 이동"

#. js-lingui-id: 2tw9bo
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Navigate to previous record"
msgstr "이전 레코드로 이동"

#. js-lingui-id: HddE65
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.ts
#~ msgid "Navigate to previous run"
#~ msgstr "이전 실행으로 이동"

#. js-lingui-id: I+Pm5V
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
msgid "Navigate to previous version"
msgstr "이전 버전으로 이동"

#. js-lingui-id: QVUN3K
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Navigate to previous workflow"
msgstr "이전 워크플로로 이동"

#. js-lingui-id: isRobC
#: src/pages/settings/data-model/SettingsNewObject.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "New"
msgstr "새로운"

#. js-lingui-id: Gntx7w
#: src/modules/command-menu/hooks/useOpenRecordInCommandMenu.ts
msgid "New {capitalizedObjectNameSingular}"
msgstr "새 {capitalizedObjectNameSingular}"

#. js-lingui-id: Kcr9Fr
#: src/modules/settings/accounts/components/SettingsNewAccountSection.tsx
msgid "New account"
msgstr "새 계정"

#. js-lingui-id: 2qr/61
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "New Approved Access Domain"
msgstr "새 승인된 액세스 도메인"

#. js-lingui-id: 8YPqRx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
msgid "New Field"
msgstr "새 필드"

#. js-lingui-id: GeB/fA
#: src/modules/settings/accounts/components/SettingsAccountsNewImapConnection.tsx
#: src/modules/settings/accounts/components/SettingsAccountsNewImapConnection.tsx
msgid "New IMAP Connection"
msgstr ""

#. js-lingui-id: o8MyXb
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
msgid "New key"
msgstr "새 키"

#. js-lingui-id: j313SZ
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
msgid "New Key"
msgstr "새 키"

#. js-lingui-id: hFxdey
#: src/pages/settings/data-model/SettingsNewObject.tsx
msgid "New Object"
msgstr "새 개체"

#. js-lingui-id: 7vhWI8
#: src/pages/auth/PasswordReset.tsx
msgid "New Password"
msgstr "새 비밀번호"

#. js-lingui-id: BcCzLv
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "New record"
msgstr "새 레코드"

#. js-lingui-id: 2lmOC5
#: src/pages/settings/roles/SettingsRoles.tsx
#~ msgid "New Role"
#~ msgstr "New Role"

#. js-lingui-id: OzWuT+
#: src/pages/settings/security/SettingsSecuritySSOIdentifyProvider.tsx
msgid "New SSO Configuration"
msgstr "새 SSO 구성"

#. js-lingui-id: C7WtCv
#: src/pages/settings/security/SettingsSecuritySSOIdentifyProvider.tsx
msgid "New SSO provider"
msgstr "새 SSO 공급자"

#. js-lingui-id: U1DAok
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhooksNew.tsx
#~ msgid "New Webhook"
#~ msgstr "New Webhook"

#. js-lingui-id: AxNmtI
#: src/modules/spreadsheet-import/steps/components/SelectSheetStep/SelectSheetStep.tsx
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
msgid "Next Step"
msgstr ""

#. js-lingui-id: QNbLNQ
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
msgid "No AI models available"
msgstr ""

#. js-lingui-id: OTe3RI
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "No change detected"
msgstr "변경 사항이 감지되지 않았습니다"

#. js-lingui-id: YFLMyY
#: src/modules/settings/admin-panel/config-variables/components/SettingsAdminConfigVariables.tsx
msgid "No config variables match your current filters. Try adjusting your filters or search criteria."
msgstr ""

#. js-lingui-id: pwenQu
#: src/modules/settings/accounts/components/SettingsAccountsListEmptyStateCard.tsx
msgid "No connected account"
msgstr "연결된 계정 없음"

#. js-lingui-id: 4BSfjK
#: src/modules/settings/data-model/fields/forms/phones/components/SettingsDataModelFieldPhonesForm.tsx
msgid "No country"
msgstr "국가 없음"

#. js-lingui-id: 7tCnBa
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateRemote.tsx
msgid "No Data Available for Remote Table"
msgstr ""

#. js-lingui-id: pxvJ9B
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
msgid "No data containing errors"
msgstr ""

#. js-lingui-id: dihZwh
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
msgid "No data found"
msgstr ""

#. js-lingui-id: xlUOps
#: src/modules/information-banner/components/billing/InformationBannerEndTrialPeriod.tsx
msgid "No free workflow executions left. End trial period and activate your billing to continue."
msgstr ""

#. js-lingui-id: pxYUeX
#: src/modules/information-banner/components/billing/InformationBannerEndTrialPeriod.tsx
msgid "No free workflow executions left. Please contact your admin."
msgstr ""

#. js-lingui-id: OcMef3
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
msgid "No latest version found"
msgstr ""

#. js-lingui-id: UwvrGq
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "No members"
msgstr "회원 없음"

#. js-lingui-id: Y4qK8/
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignment.tsx
msgid "No members assigned"
msgstr "할당된 회원 없음"

#. js-lingui-id: hfYSED
#: src/pages/settings/roles/components/RoleAssignment.tsx
#~ msgid "No members assigned to this role yet"
#~ msgstr "No members assigned to this role yet"

#. js-lingui-id: /nLvVj
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignment.tsx
msgid "No members match your search"
msgstr "검색 조건에 맞는 회원이 없습니다"

#. js-lingui-id: F9pWel
#: src/pages/settings/roles/components/RoleWorkspaceMemberPickerDropdownContent.tsx
#~ msgid "No members matching this search"
#~ msgstr "No members matching this search"

#. js-lingui-id: NluSN3
#: src/pages/settings/roles/components/RoleAssignment.tsx
#~ msgid "No members matching your search"
#~ msgstr "No members matching your search"

#. js-lingui-id: 0NudpV
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "No metrics data available"
msgstr "사용 가능한 메트릭스 데이터 없음"

#. js-lingui-id: iMCnTm
#: src/pages/settings/roles/components/RoleWorkspaceMemberPickerDropdownContent.tsx
#~ msgid "No more members to add"
#~ msgstr "No more members to add"

#. js-lingui-id: DL8pzn
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignment.tsx
#~ msgid "No more members to assign"
#~ msgstr "지정할 멤버가 더 이상 없습니다"

#. js-lingui-id: daCzI1
#: src/modules/ui/field/input/components/MultiSelectInput.tsx
msgid "No option found"
msgstr ""

#. js-lingui-id: tTItk7
#: src/modules/workflow/workflow-steps/components/WorkflowRunStepOutputDetail.tsx
msgid "No output available"
msgstr ""

#. js-lingui-id: fE7upK
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelSection.tsx
#~ msgid "No overrides found"
#~ msgstr ""

#. js-lingui-id: 5pSj4j
#: src/modules/billing/hooks/useEndSubscriptionTrialPeriod.ts
msgid "No payment method found. Please update your billing details."
msgstr ""

#. js-lingui-id: CfOZmU
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelSection.tsx
msgid "No permissions found"
msgstr ""

#. js-lingui-id: EqGTpW
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateReadOnly.tsx
msgid "No records found"
msgstr "기록을 찾을 수 없습니다"

#. js-lingui-id: 4bobEy
#: src/pages/settings/roles/components/RoleWorkspaceMemberPickerDropdownContent.tsx
#~ msgid "No Result"
#~ msgstr "No Result"

#. js-lingui-id: Ev2r9A
#: src/modules/object-record/object-filter-dropdown/components/ObjectFilterDropdownCurrencySelect.tsx
#: src/modules/object-record/object-filter-dropdown/components/ObjectFilterDropdownCountrySelect.tsx
msgid "No results"
msgstr ""

#. js-lingui-id: MA3x23
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignmentWorkspaceMemberPickerDropdownContent.tsx
msgid "No Results"
msgstr "결과 없음"

#. js-lingui-id: qq788M
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
msgid "No role"
msgstr ""

#. js-lingui-id: 9mcJ/7
#: src/modules/settings/roles/components/SettingsRolesList.tsx
msgid "No roles found"
msgstr ""

#. js-lingui-id: mmm8hk
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
msgid "No rows with errors"
msgstr ""

#. js-lingui-id: Hsl+kr
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
msgid "No Select field"
msgstr "선택 필드 없음"

#. js-lingui-id: A5XQ/A
#: src/modules/settings/admin-panel/config-variables/components/SettingsAdminConfigVariables.tsx
msgid "No variables found"
msgstr ""

#. js-lingui-id: 0uWxPM
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateTitle.ts
msgid "No workflow runs yet"
msgstr "아직 워크플로 실행 없음"

#. js-lingui-id: AQCvCC
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateTitle.ts
msgid "No workflow versions yet"
msgstr "아직 워크플로 버전 없음"

#. js-lingui-id: EdQY6l
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupFieldsContent.tsx
msgid "None"
msgstr "없음"

#. js-lingui-id: 1IipHp
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Norwegian"
msgstr "노르웨이어"

#. js-lingui-id: v3W9iu
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
msgid "Not all columns matched"
msgstr ""

#. js-lingui-id: tqk+dw
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Not available for default view"
msgstr ""

#. js-lingui-id: 0qBE9S
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
msgid "Not available on Default View"
msgstr "기본 보기에서 사용 불가"

#. js-lingui-id: 4wUkDk
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "Not empty"
msgstr "비어 있지 않음"

#. js-lingui-id: pAtylB
#: src/modules/object-record/record-field/form-types/components/VariableChip.tsx
msgid "Not Found"
msgstr "찾을 수 없음"

#. js-lingui-id: 51G4/+
#: src/modules/object-record/record-field/meta-types/display/components/ForbiddenFieldDisplay.tsx
#: src/modules/activities/calendar/components/CalendarEventNotSharedContent.tsx
msgid "Not shared"
msgstr ""

#. js-lingui-id: uzFo/5
#: src/modules/activities/timeline-activities/rows/message/components/EventCardMessageBodyNotShared.tsx
msgid "Not shared by {notSharedByFullName}"
msgstr ""

#. js-lingui-id: hZWthZ
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsRowRightContainer.tsx
msgid "Not synced"
msgstr "동기화되지 않음"

#. js-lingui-id: 1DBGsz
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Notes"
msgstr ""

#. js-lingui-id: HptUxX
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/constants/output-field-type-options.ts
#: src/modules/settings/data-model/fields/forms/number/constants/NumberDataModelSelectOptions.ts
msgid "Number"
msgstr "숫자"

#. js-lingui-id: 0fRFSb
#: src/modules/settings/data-model/fields/forms/number/components/SettingsDataModelFieldNumberForm.tsx
msgid "Number of decimals"
msgstr "소수점 자리수"

#. js-lingui-id: qg5nhQ
#: src/modules/settings/data-model/fields/forms/number/components/SettingsDataModelFieldNumberForm.tsx
msgid "Number type"
msgstr "숫자 유형"

#. js-lingui-id: W0i24j
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerManualForm.tsx
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelTableHeader.tsx
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Object"
msgstr "개체"

#. js-lingui-id: Zrauom
#: src/modules/settings/data-model/fields/forms/relation/components/SettingsDataModelFieldRelationForm.tsx
msgid "Object destination"
msgstr "개체 대상"

#. js-lingui-id: YJgmMZ
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelSection.tsx
msgid "Object-Level"
msgstr ""

#. js-lingui-id: ZXhr6E
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelSection.tsx
#~ msgid "Object-Level Permissions"
#~ msgstr ""

#. js-lingui-id: B3toQF
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
#: src/pages/settings/data-model/SettingsNewObject.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldSelect.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "Objects"
msgstr "개체"

#. js-lingui-id: KNz3EF
#: src/pages/not-found/NotFound.tsx
msgid "Off the beaten path"
msgstr "잘 알려지지 않은 길"

#. js-lingui-id: zii2Qj
#: src/modules/settings/accounts/components/SettingsAccountsCalendarVisibilitySettingsCard.tsx
msgid "Only date & participants will be shared with your team."
msgstr "날짜 및 참가자만 팀과 공유됩니다."

#. js-lingui-id: 50ETCF
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Only the timestamp & participants will be shared with your team."
msgstr "타임스탬프 및 참가자만 팀과 공유됩니다."

#. js-lingui-id: 69b7aE
#: src/modules/ui/layout/page-header/components/PageHeaderToggleCommandMenuButton.tsx
msgid "Open command menu"
msgstr "명령 메뉴 열기"

#. js-lingui-id: pZZH4Q
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "Open Gmail"
msgstr ""

#. js-lingui-id: C39K59
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutOpenInContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Open in"
msgstr "열기"

#. js-lingui-id: xGd6Ih
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "Open Outlook"
msgstr ""

#. js-lingui-id: avIMon
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerManualForm.tsx
msgid "Open the ⌘K to trigger this workflow"
msgstr ""

#. js-lingui-id: OV5wZZ
#: src/modules/object-metadata/components/NavigationDrawerOpenedSection.tsx
msgid "Opened"
msgstr "열림"

#. js-lingui-id: 4MyDFl
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Opportunities"
msgstr ""

#. js-lingui-id: hY8F2i
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Optional secret used to compute the HMAC signature for webhook payloads"
msgstr ""

#. js-lingui-id: qNELak
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Optional: Define a secret string that we will include in every webhook. Use this to authenticate and verify the webhook upon receipt."
#~ msgstr "선택 사항: 모든 Webhook에 포함시킬 비밀 문자열을 정의하십시오. 이를 사용하여 Webhook 수신 시 인증하고 검증하십시오."

#. js-lingui-id: 0zpgxV
#: src/modules/settings/roles/components/SettingsRolesDefaultRole.tsx
#: src/modules/settings/data-model/object-details/components/tabs/ObjectSettings.tsx
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectForm.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdown.tsx
msgid "Options"
msgstr "옵션"

#. js-lingui-id: BzEFor
#: src/pages/onboarding/InviteTeam.tsx
msgid "or"
msgstr "또는"

#. js-lingui-id: ucgZ0o
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Organization"
msgstr ""

#. js-lingui-id: /IX/7x
#: src/pages/settings/Releases.tsx
#: src/pages/settings/lab/SettingsLab.tsx
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/pages/settings/admin-panel/SettingsAdminConfigVariableDetails.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Other"
msgstr "기타"

#. js-lingui-id: 3V8SRM
#: src/pages/settings/admin-panel/SettingsAdminSecondaryEnvVariables.tsx
#: src/pages/settings/admin-panel/SettingsAdminSecondaryEnvVariables.tsx
#~ msgid "Other Environment Variables"
#~ msgstr "기타 환경 변수"

#. js-lingui-id: P7KMWx
#: src/modules/settings/admin-panel/components/SettingsAdminEnvVariables.tsx
#~ msgid "Other Variables"
#~ msgstr "기타 변수"

#. js-lingui-id: +vDFPm
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownWorkspacesListComponents.tsx
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Other workspaces"
msgstr "기타 워크스페이스"

#. js-lingui-id: p5ufK6
#: src/pages/onboarding/BookCallDecision.tsx
msgid "Our team can help you set up your workspace to match your specific needs and workflows."
msgstr ""

#. js-lingui-id: xvfn7l
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowOutputSchemaBuilder.tsx
msgid "Output Field {fieldNumber}"
msgstr ""

#. js-lingui-id: bv8ZsZ
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsTableRow.tsx
#~ msgid "Overridden on {isOverriddenBy} {pluralizedObject}"
#~ msgstr ""

#. js-lingui-id: boJlGf
#: src/pages/not-found/NotFound.tsx
msgid "Page Not Found"
msgstr "페이지를 찾을 수 없음"

#. js-lingui-id: 8ZsakT
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "Password"
msgstr "비밀번호"

#. js-lingui-id: BxQ79w
#: src/pages/auth/PasswordReset.tsx
msgid "Password has been updated"
msgstr "비밀번호가 업데이트되었습니다"

#. js-lingui-id: mi6Rel
#: src/modules/auth/sign-in-up/hooks/useHandleResetPassword.ts
msgid "Password reset link has been sent to the email"
msgstr "비밀번호 재설정 링크가 이메일로 전송되었습니다"

#. js-lingui-id: 1wdjme
#: src/modules/action-menu/mock/action-menu-actions.mock.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "People"
msgstr ""

#. js-lingui-id: PxBA+g
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "People I’ve sent emails to and received emails from."
msgstr "내가 이메일을 보낸 사람과 받은 사람."

#. js-lingui-id: U/UvMm
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "People I’ve sent emails to."
msgstr "내가 이메일을 보낸 사람."

#. js-lingui-id: SrVzRe
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterMenuContent.tsx
#: src/modules/object-record/record-table/record-table-footer/components/RecordTableColumnAggregateFooterDropdownContent.tsx
#: src/modules/object-record/record-board/record-board-column/components/RecordBoardColumnHeaderAggregateDropdownMenuContent.tsx
msgid "Percent"
msgstr "퍼센트"

#. js-lingui-id: yIK1GU
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Percent empty"
msgstr "비어 있는 퍼센트"

#. js-lingui-id: PWLd4c
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Percent not empty"
msgstr "비어 있지 않은 퍼센트"

#. js-lingui-id: /roQKz
#: src/modules/settings/data-model/fields/forms/number/constants/NumberDataModelSelectOptions.ts
msgid "Percentage"
msgstr "백분율"

#. js-lingui-id: Bv3y5w
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Permanently destroy record"
msgstr "레코드 영구 삭제"

#. js-lingui-id: xjWlSJ
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Permanently destroy records"
msgstr "레코드 영구 삭제"

#. js-lingui-id: 0bQ5ba
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Permanently destroy workflow"
msgstr ""

#. js-lingui-id: uKWXhB
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Permanently destroy workflows"
msgstr "워크플로 영구 삭제"

#. js-lingui-id: UjCOlM
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelTableHeader.tsx
#~ msgid "Permission overrides"
#~ msgstr ""

#. js-lingui-id: 9cDpsw
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelTableHeader.tsx
#: src/modules/settings/roles/role/components/SettingsRole.tsx
msgid "Permissions"
msgstr "권한"

#. js-lingui-id: KiuPPj
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectForm.tsx
msgid "Permissions · {objectLabelSingular}"
msgstr ""

#. js-lingui-id: qlqT9z
#: src/modules/object-record/record-field/meta-types/display/components/PhonesFieldDisplay.tsx
msgid "Phone number copied to clipboard"
msgstr "전화번호가 클립보드에 복사되었습니다"

#. js-lingui-id: FdlpPK
#: src/modules/workflow/workflow-variables/components/WorkflowVariablesDropdownObjectItems.tsx
msgid "Pick a {nameSingular} record"
msgstr ""

#. js-lingui-id: N0+GsR
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/SettingsProfile.tsx
msgid "Picture"
msgstr "사진"

#. js-lingui-id: GdgCoi
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Plan"
msgstr ""

#. js-lingui-id: 0LrFTO
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "Playground"
msgstr "플레이그라운드"

#. js-lingui-id: 90/XJz
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "Please check your email for a verification link."
msgstr ""

#. js-lingui-id: jEw0Mr
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Please enter a valid URL"
#~ msgstr "유효한 URL을 입력하세요"

#. js-lingui-id: CjNkdJ
#: src/modules/error-handler/components/AppRootErrorFallback.tsx
#: src/modules/error-handler/components/internal/AppErrorDisplay.tsx
msgid "Please refresh the page."
msgstr ""

#. js-lingui-id: X5x85V
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Please search for a user first"
msgstr "먼저 사용자를 검색하세요"

#. js-lingui-id: 6nsIo3
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Please type \"{confirmationValue}\" to confirm you want to delete this API Key. Be aware that any script using this key will stop working."
msgstr "\"{confirmationValue}\"을 입력하여 이 API 키를 삭제할 것인지 확인하세요. 이 키를 사용하는 모든 스크립트는 작동이 중지됩니다."

#. js-lingui-id: GbtYRD
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Please type \"yes\" to confirm you want to delete this webhook."
msgstr ""

#. js-lingui-id: mFZTXr
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Please type {confirmationText} to confirm you want to delete this webhook."
#~ msgstr "{confirmationText}을 입력하여 이 Webhook을 삭제할 것인지 확인하세요."

#. js-lingui-id: aRWD63
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "Please use different names for your source and destination fields"
msgstr "출처 및 목적 필드에 대해 다른 이름을 사용하십시오"

#. js-lingui-id: BPig2P
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Plural"
msgstr "복수"

#. js-lingui-id: trnWaw
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Polish"
msgstr "폴란드어"

#. js-lingui-id: MOERNx
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
#~ msgid "Portuguese"
#~ msgstr "Portuguese"

#. js-lingui-id: 0nsqwk
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Portuguese — Brazil"
msgstr "포르투갈어 - 브라질"

#. js-lingui-id: xtXHeo
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Portuguese — Portugal"
msgstr "포르투갈어 - 포르투갈"

#. js-lingui-id: R7+D0/
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
#~ msgid "Portuguese (Brazil)"
#~ msgstr "Portuguese (Brazil)"

#. js-lingui-id: 512Uma
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
#~ msgid "Portuguese (Portugal)"
#~ msgstr "Portuguese (Portugal)"

#. js-lingui-id: /IZFIg
#: src/pages/onboarding/CreateWorkspace.tsx
#~ msgid "Prefilling your workspace data"
#~ msgstr ""

#. js-lingui-id: /v6Rp9
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Prefilling your workspace data..."
msgstr ""

#. js-lingui-id: rdUucN
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectSettingsFormCard.tsx
#: src/modules/settings/data-model/components/SettingsDataModelPreviewFormCard.tsx
msgid "Preview"
msgstr "미리보기"

#. js-lingui-id: LcET2C
#: src/modules/auth/sign-in-up/components/FooterNote.tsx
msgid "Privacy Policy"
msgstr "개인정보 보호정책"

#. js-lingui-id: 3fPjUY
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Pro"
msgstr ""

#. js-lingui-id: k1ifdL
#: src/modules/spreadsheet-import/steps/components/UploadStep/components/DropZone.tsx
msgid "Processing..."
msgstr ""

#. js-lingui-id: vERlcd
#: src/pages/settings/SettingsProfile.tsx
#: src/pages/settings/SettingsProfile.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Profile"
msgstr "프로필"

#. js-lingui-id: GVxbU6
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Provide your OIDC provider details"
msgstr "OIDC 공급자 세부정보를 제공하세요"

#. js-lingui-id: YJgRqq
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Pseudo-English"
msgstr "의사 영어"

#. js-lingui-id: /IUt/5
#: src/modules/settings/admin-panel/health-status/components/WorkerHealthStatus.tsx
msgid "Queue information is not available because the worker is down"
msgstr "워크가 다운되어 대기열 정보를 사용할 수 없습니다"

#. js-lingui-id: e9OqcR
#: src/modules/settings/admin-panel/health-status/components/WorkerQueueMetricsSection.tsx
msgid "Queue performance"
msgstr "대기열 성능"

#. js-lingui-id: ibPuCP
#: src/modules/settings/developers/components/SettingsReadDocumentationButton.tsx
#~ msgid "Read documentation"
#~ msgstr "Read documentation"

#. js-lingui-id: v3xM25
#: src/modules/settings/profile/components/ChangePassword.tsx
msgid "Receive an email containing password update link"
msgstr "비밀번호 업데이트 링크가 포함된 이메일 받기"

#. js-lingui-id: ZVr1+K
#: src/modules/settings/admin-panel/health-status/components/WorkerMetricsGraph.tsx
msgid "Recent Events (oldest → newest)"
msgstr "최근 이벤트 (오래된 순 → 최신 순)"

#. js-lingui-id: gcoiFh
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Reconnect"
msgstr "다시 연결"

#. js-lingui-id: 2CUci6
#: src/modules/object-record/hooks/useBatchCreateManyRecords.ts
msgid "Record creation stopped. {formattedCreatedRecordsCount} records created."
msgstr ""

#. js-lingui-id: mj1fkT
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectIdentifiersForm.tsx
msgid "Record image"
msgstr "레코드 이미지"

#. js-lingui-id: K6/7kH
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectIdentifiersForm.tsx
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectIdentifiersForm.tsx
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectIdentifiersForm.tsx
msgid "Record label"
msgstr "레코드 레이블"

#. js-lingui-id: mAHjRd
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutOpenInContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Record Page"
msgstr "레코드 페이지"

#. js-lingui-id: dSCufP
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Record Selection"
msgstr "레코드 선택"

#. js-lingui-id: LfH+Ea
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Redirect Url copied to clipboard"
msgstr "리디렉션 URL이 클립보드에 복사되었습니다"

#. js-lingui-id: RZjynQ
#: src/modules/settings/security/components/SSO/SettingsSSOOIDCForm.tsx
msgid "Redirection URI"
msgstr "리디렉션 URI"

#. js-lingui-id: vpZcGd
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Regenerate an API key"
msgstr "API 키 재생성"

#. js-lingui-id: Mwqo5m
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Regenerate key"
msgstr "키 재생성"

#. js-lingui-id: D+Mv78
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "Regenerate Key"
msgstr "키 재생성"

#. js-lingui-id: UiAJoB
#: src/modules/settings/data-model/fields/forms/relation/components/SettingsDataModelFieldRelationForm.tsx
msgid "Relation type"
msgstr "관계 유형"

#. js-lingui-id: HR+PwH
#: src/modules/settings/data-model/fields/forms/date/utils/getDisplayFormatLabel.ts
msgid "Relative"
msgstr ""

#. js-lingui-id: 5icoS1
#: src/pages/settings/Releases.tsx
#: src/pages/settings/Releases.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Releases"
msgstr "릴리스"

#. js-lingui-id: HpK/8d
#: src/pages/settings/workspace/SettingsCustomDomain.tsx
msgid "Reload"
msgstr "새로 고침"

#. js-lingui-id: t/YqKh
#: src/modules/ui/input/components/ImageInput.tsx
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
msgid "Remove"
msgstr "제거"

#. js-lingui-id: ken+P9
#: src/pages/settings/roles/components/RoleAssignmentConfirmationModal.tsx
#~ msgid "Remove {workspaceMemberName}?"
#~ msgstr "Remove {workspaceMemberName}?"

#. js-lingui-id: 1O32oy
#: src/modules/settings/accounts/components/SettingsAccountsRowDropdownMenu.tsx
msgid "Remove account"
msgstr "계정 제거"

#. js-lingui-id: Q2u5E9
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectFormOptionRow.tsx
msgid "Remove as default"
msgstr "기본으로 설정 해제"

#. js-lingui-id: T/pF0Z
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Remove from favorites"
msgstr "즐겨찾기에서 제거"

#. js-lingui-id: 00Lxnh
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectFormOptionRow.tsx
msgid "Remove option"
msgstr "옵션 제거"

#. js-lingui-id: Ee8JBW
#: src/modules/workflow/workflow-steps/workflow-actions/form-action/components/WorkflowEditActionFormBuilder.tsx
msgid "Reorder field"
msgstr ""

#. js-lingui-id: G42SNI
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "Resend email"
msgstr ""

#. js-lingui-id: OfhWJH
#: src/pages/settings/admin-panel/SettingsAdminConfigVariableDetails.tsx
#: src/modules/views/components/ViewBarDetails.tsx
msgid "Reset"
msgstr "초기화"

#. js-lingui-id: KbS2K9
#: src/pages/auth/PasswordReset.tsx
msgid "Reset Password"
msgstr "비밀번호 재설정"

#. js-lingui-id: 1IWc1n
#: src/modules/command-menu/components/ResetContextToSelectionCommandButton.tsx
msgid "Reset to"
msgstr "다음으로 재설정"

#. js-lingui-id: Tj36Dr
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableActionButtons.tsx
msgid "Reset to Default"
msgstr ""

#. js-lingui-id: 6rzsES
#: src/pages/settings/admin-panel/SettingsAdminConfigVariableDetails.tsx
msgid "Reset variable"
msgstr ""

#. js-lingui-id: WHiaOl
#: src/pages/settings/developers/playground/SettingsRestPlayground.tsx
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "REST"
msgstr "휴식"

#. js-lingui-id: 6z9W13
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
msgid "Restart"
msgstr ""

#. js-lingui-id: 82zWno
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
msgid "Restart Import"
msgstr ""

#. js-lingui-id: yKu/3Y
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Restore"
msgstr "복원"

#. js-lingui-id: y4Ib1n
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Restore record"
msgstr "레코드 복원"

#. js-lingui-id: Vw369F
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Restore records"
msgstr "레코드 복원"

#. js-lingui-id: kx0s+n
#: src/modules/command-menu/hooks/useCommandMenuSearchRecords.tsx
msgid "Results"
msgstr "결과"

#. js-lingui-id: BohSvm
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
#~ msgid "Review your import"
#~ msgstr ""

#. js-lingui-id: Wz7mbi
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsTableRow.tsx
msgid "Revoked for {revokedBy} {pluralizedRevokedObject}"
msgstr ""

#. js-lingui-id: GG37FL
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevelTableRow.tsx
msgid "Revoked for this object"
msgstr ""

#. js-lingui-id: 4+uhWL
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsTableRow.tsx
#~ msgid "Revoked on {revokedBy} {pluralizedObject}"
#~ msgstr ""

#. js-lingui-id: iSfzXo
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettings.tsx
#: src/modules/settings/roles/role/components/SettingsRoleLabelContainer.tsx
msgid "Role name"
msgstr ""

#. js-lingui-id: gF4nBH
#: src/modules/settings/roles/role/components/SettingsRole.tsx
msgid "Role name cannot be empty"
msgstr ""

#. js-lingui-id: 5dJK4M
#: src/pages/settings/roles/SettingsRoleAddObjectLevel.tsx
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectForm.tsx
#: src/modules/settings/roles/components/SettingsRolesContainer.tsx
#: src/modules/settings/roles/components/SettingsRolesContainer.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Roles"
msgstr "역할"

#. js-lingui-id: uJc01W
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Romanian"
msgstr "루마니아어"

#. js-lingui-id: UX0N2y
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateSubTitle.ts
msgid "Run a workflow and return here to view its executions"
msgstr "워크플로를 실행하고 여기로 돌아와 실행 결과를 확인하세요"

#. js-lingui-id: nji0/X
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Russian"
msgstr "러시아어"

#. js-lingui-id: tfDRzk
#: src/modules/settings/components/SaveAndCancelButtons/SaveButton.tsx
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableActionButtons.tsx
msgid "Save"
msgstr "저장"

#. js-lingui-id: apLRCm
#: src/modules/views/components/UpdateViewButtonGroup.tsx
msgid "Save as new view"
msgstr "새 보기로 저장"

#. js-lingui-id: QJ8HBJ
#: src/modules/settings/playground/components/PlaygroundSetupForm.tsx
msgid "Schema"
msgstr "스키마"

#. js-lingui-id: A1taO8
#: src/modules/views/components/ViewBarFilterDropdownVectorSearchInput.tsx
#: src/modules/views/components/ViewBarFilterDropdownVectorSearchButton.tsx
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownWorkspacesListComponents.tsx
#: src/modules/sign-in-background-mock/components/SignInAppNavigationDrawerMock.tsx
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignmentWorkspaceMemberPickerDropdown.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#: src/modules/navigation/components/MainNavigationDrawerFixedItems.tsx
#: src/modules/command-menu/hooks/useOpenRecordsSearchPageInCommandMenu.ts
#: src/modules/action-menu/actions/record-agnostic-actions/constants/RecordAgnosticActionsConfig.tsx
#: src/modules/action-menu/actions/record-agnostic-actions/constants/RecordAgnosticActionsConfig.tsx
msgid "Search"
msgstr "검색"

#. js-lingui-id: 8NBMeZ
#: src/modules/command-menu/components/CommandMenu.tsx
msgid "Search ''{commandMenuSearch}'' with..."
msgstr "''{commandMenuSearch}''으로 검색..."

#. js-lingui-id: l1/uy2
#: src/pages/settings/data-model/SettingsObjectFieldTable.tsx
msgid "Search a field..."
msgstr "필드 검색..."

#. js-lingui-id: t3n1Qy
#: src/modules/settings/roles/role-assignment/components/RoleAssignment.tsx
#~ msgid "Search a member"
#~ msgstr "Search a member"

#. js-lingui-id: xdl79x
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Search a team member..."
msgstr "팀원 검색..."

#. js-lingui-id: lnDfeK
#: src/modules/settings/data-model/fields/forms/components/SettingsObjectNewFieldSelector.tsx
msgid "Search a type"
msgstr "유형 검색"

#. js-lingui-id: x55IVv
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignment.tsx
msgid "Search an assigned team member..."
msgstr "할당된 팀원 검색..."

#. js-lingui-id: k7kp5/
#: src/pages/settings/data-model/SettingsObjectIndexTable.tsx
msgid "Search an index..."
msgstr "색인 검색..."

#. js-lingui-id: pFe0YS
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelObjectPicker.tsx
msgid "Search an object"
msgstr ""

#. js-lingui-id: 3UPqHL
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableSearchInput.tsx
msgid "Search config variables"
msgstr ""

#. js-lingui-id: WLfEv1
#: src/modules/object-record/object-filter-dropdown/components/ObjectFilterDropdownCountrySelect.tsx
msgid "Search country"
msgstr ""

#. js-lingui-id: sSARNY
#: src/modules/object-record/object-filter-dropdown/components/ObjectFilterDropdownCurrencySelect.tsx
msgid "Search currency"
msgstr ""

#. js-lingui-id: 7taA9j
#: src/modules/views/components/ViewBarFilterDropdownFieldSelectMenu.tsx
#: src/modules/spreadsheet-import/components/MatchColumnSelectFieldSelectDropdownContent.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupFieldsContent.tsx
#: src/modules/object-record/advanced-filter/components/AdvancedFilterFieldSelectSearchInput.tsx
msgid "Search fields"
msgstr "필드 검색"

#. js-lingui-id: ofuw3g
#: src/pages/settings/data-model/SettingsObjects.tsx
msgid "Search for an object..."
msgstr "개체 검색..."

#. js-lingui-id: lnwW17
#: src/modules/ui/input/components/IconPicker.tsx
msgid "Search icon"
msgstr "검색 아이콘"

#. js-lingui-id: IMeaSJ
#: src/modules/action-menu/actions/record-agnostic-actions/constants/RecordAgnosticActionsConfig.tsx
#: src/modules/action-menu/actions/record-agnostic-actions/constants/RecordAgnosticActionsConfig.tsx
msgid "Search records"
msgstr "레코드 검색"

#. js-lingui-id: 8sgZS9
#: src/modules/billing/components/SubscriptionPrice.tsx
msgid "seat / month"
msgstr ""

#. js-lingui-id: aQnWwf
#: src/modules/billing/components/SubscriptionPrice.tsx
msgid "seat / month - billed yearly"
msgstr ""

#. js-lingui-id: grt0Pu
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Seats"
msgstr ""

#. js-lingui-id: 8VEDbV
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Secret"
msgstr "비밀"

#. js-lingui-id: e1v+J3
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Secret (optional)"
msgstr ""

#. js-lingui-id: a3LDKx
#: src/pages/settings/security/SettingsSecuritySSOIdentifyProvider.tsx
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
#: src/pages/settings/security/SettingsSecurity.tsx
#: src/pages/settings/security/SettingsSecurity.tsx
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "Security"
msgstr "보안"

#. js-lingui-id: i0zibG
#: src/modules/settings/roles/role-permissions/object-level-permissions/utils/objectPermissionKeyToHumanReadableText.ts
msgid "see"
msgstr ""

#. js-lingui-id: rJnbMG
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
msgid "See {objectLabel}"
msgstr ""

#. js-lingui-id: QREcJS
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "See active version"
msgstr "활성 버전 보기"

#. js-lingui-id: BxIUpp
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "See deleted records"
msgstr "삭제된 레코드 보기"

#. js-lingui-id: Z+SGGW
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
msgid "See deleted runs"
msgstr "삭제된 실행 보기"

#. js-lingui-id: 91fiCe
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.ts
#~ msgid "See deleted versions"
#~ msgstr "삭제된 버전 보기"

#. js-lingui-id: 5jZIe5
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "See deleted workflows"
msgstr "삭제된 워크플로 보기"

#. js-lingui-id: FvBwjM
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevel.tsx
#~ msgid "See Records on {objectLabel}"
#~ msgstr ""

#. js-lingui-id: irbH/8
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsSection.tsx
msgid "See Records on All Objects"
msgstr ""

#. js-lingui-id: OpPn5Z
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "See runs"
msgstr "실행 보기"

#. js-lingui-id: gGEfj/
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
msgid "See version"
msgstr "버전 보기"

#. js-lingui-id: EtyY4+
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "See versions"
msgstr "버전 보기"

#. js-lingui-id: lYhPN0
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "See versions history"
msgstr "버전 기록 보기"

#. js-lingui-id: qEKLJV
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowRunsActionsConfig.tsx
msgid "See workflow"
msgstr "워크플로우 보기"

#. js-lingui-id: XThiR2
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "See workflows"
msgstr "워크플로우들 보기"

#. js-lingui-id: 5S/Gqz
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerManualForm.tsx
msgid "Select a record then open the ⌘K to trigger this workflow"
msgstr ""

#. js-lingui-id: WZvt/6
#: src/modules/command-menu/hooks/useWorkflowCommandMenu.ts
#: src/modules/command-menu/hooks/__tests__/useWorkflowCommandMenu.test.tsx
msgid "Select Action"
msgstr "작업 선택"

#. js-lingui-id: hVPa4O
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/components/UnmatchColumn.tsx
msgid "Select an option"
msgstr ""

#. js-lingui-id: 21YIUv
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/components/TemplateColumn.tsx
msgid "Select column..."
msgstr ""

#. js-lingui-id: J6WI1B
#: src/modules/spreadsheet-import/steps/components/UploadStep/components/DropZone.tsx
msgid "Select file"
msgstr ""

#. js-lingui-id: llc/hA
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableOptionsDropdownContent.tsx
msgid "Select Group"
msgstr ""

#. js-lingui-id: ljSvAs
#: src/modules/spreadsheet-import/steps/components/SelectHeaderStep/SelectHeaderStep.tsx
msgid "Select header row"
msgstr ""

#. js-lingui-id: UKCiEd
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableOptionsDropdownContent.tsx
msgid "Select Source"
msgstr ""

#. js-lingui-id: mWHn81
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
#~ msgid "Select the correct field for each column you'd like to import."
#~ msgstr ""

#. js-lingui-id: pofGCP
#: src/modules/settings/data-model/fields/forms/boolean/components/SettingsDataModelFieldBooleanForm.tsx
msgid "Select the default value for this boolean field"
msgstr "이 boolean 필드의 기본값 선택"

#. js-lingui-id: xraglu
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Select the events you wish to send to this endpoint"
msgstr "이 엔드포인트로 보내려는 이벤트를 선택하세요"

#. js-lingui-id: +xn1pe
#: src/modules/spreadsheet-import/steps/components/SelectSheetStep/SelectSheetStep.tsx
msgid "Select the sheet to use"
msgstr ""

#. js-lingui-id: AXTJAW
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
msgid "Select your preferred language"
msgstr "선호하는 언어 선택"

#. js-lingui-id: mjK8F3
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Send an invite email to your team"
msgstr "팀에 초대 이메일 보내기"

#. js-lingui-id: IoAuJG
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "Sending..."
msgstr ""

#. js-lingui-id: h69WC6
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "Sent"
msgstr "보냄"

#. js-lingui-id: hdVMzO
#: src/modules/settings/accounts/components/SettingsAccountsMessageAutoCreationCard.tsx
msgid "Sent and Received"
msgstr "보낸 및 받은"

#. js-lingui-id: 6oxz/y
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Serbian (Cyrillic)"
msgstr "세르비아어 (키릴)"

#. js-lingui-id: WKimFU
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#~ msgid "Server Admin"
#~ msgstr "Server Admin"

#. js-lingui-id: yy5k7a
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/pages/settings/admin-panel/SettingsAdmin.tsx
#~ msgid "Server Admin Panel"
#~ msgstr "Server Admin Panel"

#. js-lingui-id: LUc0oL
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Service Provider Details"
msgstr "서비스 공급자 세부 정보"

#. js-lingui-id: YZwx1e
#: src/modules/settings/roles/components/SettingsRolesDefaultRole.tsx
msgid "Set a default role for this workspace"
msgstr "이 작업 공간의 기본 역할 설정"

#. js-lingui-id: M97je1
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelSection.tsx
#~ msgid "Set additional object-level permissions"
#~ msgstr ""

#. js-lingui-id: PPcets
#: src/modules/settings/data-model/fields/forms/select/components/SettingsDataModelFieldSelectFormOptionRow.tsx
msgid "Set as default"
msgstr "기본값으로 설정"

#. js-lingui-id: V7fgiB
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
msgid "Set email visibility, manage your blocklist and more."
msgstr "이메일 공개 여부 설정, 차단 목록 관리 등을 할 수 있습니다."

#. js-lingui-id: 6KA8xy
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Set global workspace preferences"
msgstr ""

#. js-lingui-id: qNbuWB
#: src/pages/settings/workspace/SettingsCustomDomain.tsx
msgid "Set the name of your custom domain and configure your DNS records."
msgstr "사용자 지정 도메인의 이름을 설정하고 DNS 레코드를 구성하십시오."

#. js-lingui-id: cx14rp
#: src/pages/settings/workspace/SettingsCustomDomain.tsx
#~ msgid "Set the name of your domain"
#~ msgstr "Set the name of your domain"

#. js-lingui-id: tn41zE
#: src/pages/settings/workspace/SettingsSubdomain.tsx
msgid "Set the name of your subdomain"
msgstr "하위 도메인 이름 설정"

#. js-lingui-id: oG3fDz
#: src/pages/onboarding/CreateWorkspace.tsx
#~ msgid "Setting up your database"
#~ msgstr ""

#. js-lingui-id: dQsqpC
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Setting up your database..."
msgstr ""

#. js-lingui-id: Tz0i8g
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
#: src/modules/sign-in-background-mock/components/SignInAppNavigationDrawerMock.tsx
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
#: src/modules/settings/roles/role/components/SettingsRole.tsx
#: src/modules/settings/accounts/components/SettingsAccountsSettingsSection.tsx
#: src/modules/settings/accounts/components/SettingsAccountsNewImapConnection.tsx
#: src/modules/settings/accounts/components/SettingsAccountsEditImapConnection.tsx
#: src/modules/navigation/components/MainNavigationDrawerFixedItems.tsx
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Settings"
msgstr "설정"

#. js-lingui-id: uXW4pg
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Settings All Access"
msgstr ""

#. js-lingui-id: p8fNBm
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Settings permissions"
msgstr "설정 권한"

#. js-lingui-id: Vy9kmk
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "Share this link to invite users to join your workspace"
msgstr "이 링크를 공유하여 사용자를 워크스페이스에 초대하세요"

#. js-lingui-id: RRXpo1
#: src/modules/settings/data-model/fields/forms/number/constants/NumberDataModelSelectOptions.ts
msgid "Short"
msgstr ""

#. js-lingui-id: gWk8gY
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Should changing a field's label also change the API name?"
msgstr "필드의 레이블을 변경하면 API 이름도 변경되어야 하나요?"

#. js-lingui-id: WFtdWr
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Should changing an object's label also change the API?"
msgstr "객체의 레이블을 변경할 때 API도 변경되어야 하나요?"

#. js-lingui-id: 1OVrop
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableOptionsDropdownContent.tsx
msgid "Show hidden groups"
msgstr ""

#. js-lingui-id: qi+g0a
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
msgid "Show only rows with errors"
msgstr ""

#. js-lingui-id: MHlGJL
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
#~ msgid "Show unmatched columns"
#~ msgstr ""

#. js-lingui-id: uVg8dY
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutOpenInContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Side Panel"
msgstr "사이드 패널"

#. js-lingui-id: 5lWFkC
#: src/modules/auth/sign-in-up/components/internal/SignInUpWithCredentials.tsx
msgid "Sign in"
msgstr "로그인"

#. js-lingui-id: n1ekoW
#: src/modules/auth/sign-in-up/components/SignInUpGlobalScopeForm.tsx
msgid "Sign In"
msgstr ""

#. js-lingui-id: e+RpCP
#: src/modules/auth/sign-in-up/components/internal/SignInUpWithCredentials.tsx
msgid "Sign up"
msgstr "가입하기"

#. js-lingui-id: mErq7F
#: src/modules/auth/sign-in-up/components/SignInUpGlobalScopeForm.tsx
msgid "Sign Up"
msgstr ""

#. js-lingui-id: 5v3IHX
#: src/modules/auth/sign-in-up/components/internal/SignInUpWithSSO.tsx
msgid "Single sign-on (SSO)"
msgstr "싱글 사인온(SSO)"

#. js-lingui-id: maCaRp
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Singular"
msgstr "단수형"

#. js-lingui-id: djfBXF
#: src/modules/settings/data-model/validation-schemas/settingsDataModelObjectAboutFormSchema.ts
msgid "Singular and plural labels must be different"
msgstr "단수와 복수 레이블은 달라야 합니다"

#. js-lingui-id: zvwLTy
#: src/modules/settings/data-model/validation-schemas/settingsDataModelObjectAboutFormSchema.ts
msgid "Singular and plural names must be different"
msgstr "단수와 복수 이름은 달라야 합니다"

#. js-lingui-id: 6Uau97
#: src/pages/onboarding/InviteTeam.tsx
#: src/pages/onboarding/BookCall.tsx
msgid "Skip"
msgstr "건너뛰기"

#. js-lingui-id: f6Hub0
#: src/modules/object-record/record-table/record-table-header/components/RecordTableColumnHeadDropdownMenu.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupsContent.tsx
msgid "Sort"
msgstr "정렬"

#. js-lingui-id: wdxz7K
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableOptionsDropdownContent.tsx
msgid "Source"
msgstr ""

#. js-lingui-id: 65A04M
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Spanish"
msgstr "스페인어"

#. js-lingui-id: vnS6Rf
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "SSO"
msgstr "SSO"

#. js-lingui-id: vlvAkg
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "SSO (SAML / OIDC)"
msgstr "SSO (SAML / OIDC)"

#. js-lingui-id: ErU1td
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
msgid "Stages"
msgstr "단계"

#. js-lingui-id: TJBHlP
#: src/modules/settings/roles/role-permissions/object-level-permissions/components/SettingsRolePermissionsObjectLevelObjectPicker.tsx
msgid "Standard"
msgstr ""

#. js-lingui-id: uAQUqI
#: src/pages/settings/admin-panel/SettingsAdminIndicatorHealthStatus.tsx
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Status"
msgstr "상태"

#. js-lingui-id: qn1Xxf
#: src/modules/settings/admin-panel/config-variables/utils/useSourceContent.ts
msgid "Stored in database"
msgstr ""

#. js-lingui-id: ku9TbG
#: src/pages/settings/workspace/SettingsSubdomain.tsx
msgid "Subdomain"
msgstr "하위 도메인"

#. js-lingui-id: omhc+7
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Subdomain already taken"
msgstr "이미 사용 중인 하위 도메인"

#. js-lingui-id: OlC/tU
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Subdomain can not be longer than 30 characters"
msgstr "하위 도메인은 30자를 초과할 수 없습니다"

#. js-lingui-id: ZETwlU
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Subdomain can not be shorter than 3 characters"
msgstr "하위 도메인은 3자보다 짧을 수 없습니다"

#. js-lingui-id: DTG2nE
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Subdomain updated"
msgstr "하위 도메인이 업데이트되었습니다"

#. js-lingui-id: 97eDN2
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Subject and metadata"
msgstr "주제와 메타데이터"

#. js-lingui-id: LYbP/A
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
msgid "Subject and metadata will be shared with your team."
msgstr "주제와 메타데이터가 팀과 공유됩니다."

#. js-lingui-id: DvSvG9
#: src/modules/activities/timeline-activities/rows/message/components/EventCardMessageForbidden.tsx
msgid "Subject not shared"
msgstr ""

#. js-lingui-id: J9ylmk
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
msgid "Subject, body and attachments will be shared with your team."
msgstr "주제, 본문, 첨부 파일이 팀과 공유됩니다."

#. js-lingui-id: hQRttt
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
msgid "Submit"
msgstr ""

#. js-lingui-id: EDl9kS
#: src/modules/information-banner/components/billing/InformationBannerNoBillingSubscription.tsx
msgid "Subscribe"
msgstr ""

#. js-lingui-id: WVzGc2
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Subscription"
msgstr ""

#. js-lingui-id: uvY7Wk
#: src/modules/billing/hooks/useEndSubscriptionTrialPeriod.ts
msgid "Subscription activated."
msgstr ""

#. js-lingui-id: B5jRKH
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Subscription has been switched {to}"
#~ msgstr "구독이 {to}로 전환되었습니다"

#. js-lingui-id: KNMVB5
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Subscription has been switched to Organization Plan."
msgstr ""

#. js-lingui-id: TXH0ad
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Subscription has been switched to yearly."
#~ msgstr ""

#. js-lingui-id: A5YO8f
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Subscription has been switched to Yearly."
msgstr ""

#. js-lingui-id: nyQWMb
#: src/modules/spreadsheet-import/components/MatchColumnSelectFieldSelectDropdownContent.tsx
msgid "Suggested"
msgstr ""

#. js-lingui-id: AxQiPW
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationLabel.ts
msgid "Sum"
msgstr "합계"

#. js-lingui-id: XYLcNv
#: src/pages/settings/security/SettingsSecurity.tsx
msgid "Support"
msgstr "지원"

#. js-lingui-id: UaISq3
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Swedish"
msgstr "스웨덴어"

#. js-lingui-id: 9yk9d1
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Switch {from}"
#~ msgstr "{from}에서 전환"

#. js-lingui-id: qi74XZ
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Switch {to}"
#~ msgstr "{to}로 전환"

#. js-lingui-id: L6Fg36
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Switch billing {to}"
#~ msgstr "청구 {to}로 전환"

#. js-lingui-id: o04bjR
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Switch billing to yearly"
#~ msgstr ""

#. js-lingui-id: /IYUDq
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Switch from monthly to yearly"
#~ msgstr ""

#. js-lingui-id: E4rPXj
#: src/pages/onboarding/ChooseYourPlan.tsx
#~ msgid "Switch Plan"
#~ msgstr ""

#. js-lingui-id: lz+f/E
#: src/pages/onboarding/ChooseYourPlan.tsx
#~ msgid "Switch to {alternatePlanName}"
#~ msgstr "{alternatePlanName}로 전환"

#. js-lingui-id: GCMizN
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Switch to Organization"
msgstr ""

#. js-lingui-id: 1EwZPZ
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Switch to yearly"
#~ msgstr ""

#. js-lingui-id: eCX1DT
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Switch to Yearly"
msgstr ""

#. js-lingui-id: 5TRY4+
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsRowRightContainer.tsx
msgid "Sync failed"
msgstr "동기화 실패"

#. js-lingui-id: N2FcBE
#: src/modules/settings/accounts/components/SettingsAccountsConnectedAccountsRowRightContainer.tsx
msgid "Synced"
msgstr "동기화됨"

#. js-lingui-id: AtzMpB
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldIconLabelForm.tsx
msgid "Synchronize Field Label and API Name"
msgstr "필드 레이블과 API 이름 동기화"

#. js-lingui-id: WZ6bN9
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
msgid "Synchronize Objects Labels and API Names"
msgstr "객체 레이블과 API 이름 동기화"

#. js-lingui-id: D4SseJ
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeZoneSelect.tsx
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeZoneSelect.tsx
msgid "System settings"
msgstr "시스템 설정"

#. js-lingui-id: E3AMmw
#: src/pages/settings/profile/appearance/components/DateTimeSettingsDateFormatSelect.tsx
msgid "System settings - {systemDateFormatLabel}"
msgstr "시스템 설정 - {systemDateFormatLabel}"

#. js-lingui-id: 0ZgB1e
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeFormatSelect.tsx
msgid "System Settings - {systemTimeFormatLabel}"
msgstr "시스템 설정 - {systemTimeFormatLabel}"

#. js-lingui-id: 4hJhzz
#: src/modules/views/view-picker/constants/ViewPickerTypeSelectOptions.ts
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownLayoutContent.tsx
msgid "Table"
msgstr "테이블"

#. js-lingui-id: GtycJ/
#: src/modules/action-menu/actions/record-actions/constants/DefaultRecordActionsConfig.tsx
msgid "Tasks"
msgstr ""

#. js-lingui-id: xowcRf
#: src/modules/auth/sign-in-up/components/FooterNote.tsx
msgid "Terms of Service"
msgstr "서비스 약관"

#. js-lingui-id: NnH3pK
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Test"
msgstr "테스트"

#. js-lingui-id: bU9B27
#: src/modules/action-menu/actions/record-actions/constants/WorkflowActionsConfig.tsx
msgid "Test Workflow"
msgstr "테스트 워크플로"

#. js-lingui-id: xeiujy
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/constants/output-field-type-options.ts
msgid "Text"
msgstr "텍스트"

#. js-lingui-id: lyaiTc
#: src/modules/object-record/record-field/components/LightCopyIconButton.tsx
msgid "Text copied to clipboard"
msgstr "텍스트가 클립보드에 복사되었습니다"

#. js-lingui-id: 1xQkU9
#: src/modules/settings/data-model/fields/forms/phones/components/SettingsDataModelFieldPhonesForm.tsx
msgid "The default country code for new phone numbers."
msgstr "새 전화번호의 기본 국가 코드."

#. js-lingui-id: PmXLtL
#: src/modules/settings/data-model/fields/forms/address/components/SettingsDataModelFieldAddressForm.tsx
msgid "The default country for new addresses"
msgstr "새 주소의 기본 국가"

#. js-lingui-id: 2OUtmv
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "The description of this field"
msgstr "이 필드의 설명"

#. js-lingui-id: VGZYbZ
#: src/pages/settings/SettingsProfile.tsx
msgid "The email associated to your account"
msgstr "계정에 연결된 이메일"

#. js-lingui-id: diO3fm
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerManualForm.tsx
msgid "The icon your workflow trigger will display in the command menu"
msgstr ""

#. js-lingui-id: h8mvCd
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "The name and icon of this field"
msgstr "이 필드의 이름과 아이콘"

#. js-lingui-id: e/dfFe
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
msgid "The name of your connection"
msgstr "연결의 이름"

#. js-lingui-id: 8EkdZh
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "The name of your Domain"
msgstr "도메인의 이름"

#. js-lingui-id: +C8Rdp
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "The name of your organization"
msgstr "조직의 이름"

#. js-lingui-id: L97sPr
#: src/pages/not-found/NotFound.tsx
msgid "The page you're seeking is either gone or never was. Let's get you back on track"
msgstr "찾고 계신 페이지는 이미 사라졌거나 존재하지 않습니다. 다시 정상화해 드리겠습니다"

#. js-lingui-id: uWikAA
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "The values of this field"
msgstr "이 필드의 값"

#. js-lingui-id: +69KDk
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
msgid "The values of this field must be unique"
msgstr "이 필드의 값은 고유해야 합니다"

#. js-lingui-id: MHLapp
#: src/modules/settings/accounts/components/SettingsAccountsCalendarVisibilitySettingsCard.tsx
msgid "The whole event details will be shared with your team."
msgstr "전체 이벤트 세부정보가 팀과 공유됩니다."

#. js-lingui-id: MssRHl
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignment.tsx
msgid "The workspace needs at least one Admin"
msgstr ""

#. js-lingui-id: FEr96N
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownThemesComponents.tsx
msgid "Theme"
msgstr "테마"

#. js-lingui-id: 6tNbxl
#: src/modules/ui/navigation/navigation-drawer/components/MultiWorkspaceDropdown/internal/MultiWorkspaceDropdownDefaultComponents.tsx
msgid "Theme "
msgstr "테마 "

#. js-lingui-id: /cSDOy
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
msgid "There are required columns that are not matched or ignored. Do you want to continue?"
msgstr ""

#. js-lingui-id: luL9RX
#: src/modules/spreadsheet-import/steps/components/ValidationStep/ValidationStep.tsx
msgid "There are still some rows that contain errors. Rows with errors will be ignored when submitting."
msgstr ""

#. js-lingui-id: ynfkhb
#: src/pages/auth/PasswordReset.tsx
msgid "There was an error while updating password."
msgstr "비밀번호 업데이트 중 오류가 발생했습니다."

#. js-lingui-id: PmtLRf
#: src/modules/auth/sign-in-up/hooks/useHandleResetPassword.ts
#: src/modules/auth/sign-in-up/hooks/useHandleResendEmailVerificationToken.ts
#~ msgid "There was an issue"
#~ msgstr "문제가 발생했습니다"

#. js-lingui-id: DHjmMm
#: src/modules/settings/admin-panel/components/SettingsAdminEnvVariables.tsx
#~ msgid "These are only the server values. Ensure your worker environment has the same variables and values, this is required for asynchronous tasks like email sync."
#~ msgstr "이것들은 서버 값에 불과합니다. 작업 환경에 동일한 변수와 값이 있는지 확인하십시오. 이메일 동기화와 같은 비동기 작업에 필요합니다."

#. js-lingui-id: Kb1oAw
#: src/modules/object-record/spreadsheet-import/utils/spreadsheetImportGetUnicityRowHook.ts
msgid "This {columnName} value already exists in your import data"
msgstr ""

#. js-lingui-id: hqCwGc
#: src/pages/settings/SettingsWorkspaceMembers.tsx
msgid "This action cannot be undone. This will permanently delete this user and remove them from all their assignments."
msgstr "이 작업은 취소할 수 없습니다. 이렇게 하면 사용자가 영구적으로 삭제되고 모든 할당에서 제거됩니다."

#. js-lingui-id: gWGuHC
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
msgid "This action cannot be undone. This will permanently delete your entire workspace. <0/> Please type in your email to confirm."
msgstr "이 작업은 취소할 수 없습니다. 이렇게 하면 전체 워크스페이스가 영구적으로 삭제됩니다. <0/> 확인하려면 이메일을 입력하세요."

#. js-lingui-id: SLIRqz
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "This database value overrides environment settings. "
msgstr ""

#. js-lingui-id: yHIStW
#: src/pages/settings/roles/components/RoleAssignmentConfirmationModalSubtitle.tsx
#~ msgid "This member will be unassigned from this role."
#~ msgstr "This member will be unassigned from this role."

#. js-lingui-id: vbLBMd
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectFormObjectLevelTableRow.tsx
msgid "This role can {humanReadableAction} all records"
msgstr ""

#. js-lingui-id: /tr8Uy
#: src/pages/settings/roles/components/RolePermissions.tsx
#~ msgid "This Role has the following permissions."
#~ msgstr "This Role has the following permissions."

#. js-lingui-id: 6j5nJX
#: src/pages/settings/roles/components/RoleAssignment.tsx
#~ msgid "This Role is assigned to these workspace member."
#~ msgstr "This Role is assigned to these workspace member."

#. js-lingui-id: xPfDRx
#: src/modules/settings/roles/role-assignment/components/SettingsRoleAssignment.tsx
msgid "This role is assigned to these workspace members."
msgstr "이 역할은 이 워크스페이스 멤버들에게 할당되었습니다."

#. js-lingui-id: C/wr0z
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "This setting can only be configured through environment variables."
msgstr ""

#. js-lingui-id: iAubP2
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "This should never happen"
msgstr ""

#. js-lingui-id: MMGDfe
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "This value will be saved to the database."
msgstr ""

#. js-lingui-id: yByRxz
#: src/modules/settings/admin-panel/health-status/constants/WorkerQueueMetricsSelectOptions.ts
msgid "This week"
msgstr "이번 주"

#. js-lingui-id: 4YifBe
#: src/pages/settings/admin-panel/SettingsAdminConfigVariableDetails.tsx
msgid "This will revert the database value to environment/default value. The database override will be removed and the system will use the environment settings."
msgstr ""

#. js-lingui-id: n9nSNJ
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeFormatSelect.tsx
msgid "Time format"
msgstr "시간 형식"

#. js-lingui-id: Mz2JN2
#: src/pages/settings/profile/appearance/components/DateTimeSettingsTimeZoneSelect.tsx
msgid "Time zone"
msgstr "시간대"

#. js-lingui-id: /VwdtK
#: src/modules/settings/accounts/components/SettingsAccountsMessageVisibilityCard.tsx
msgid "Timestamp and participants will be shared with your team."
msgstr "타임스탬프와 참가자가 팀과 공유됩니다."

#. js-lingui-id: aqMzDX
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "to monthly"
#~ msgstr "월간으로"

#. js-lingui-id: WXXiXO
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "to yearly"
#~ msgstr "연간으로"

#. js-lingui-id: ecUA8p
#: src/modules/settings/admin-panel/health-status/constants/WorkerQueueMetricsSelectOptions.ts
#: src/modules/localization/utils/formatDateISOStringToRelativeDate.ts
msgid "Today"
msgstr "오늘"

#. js-lingui-id: XArpJK
#: src/modules/settings/roles/role-permissions/objects-permissions/components/SettingsRolePermissionsObjectsTableHeader.tsx
msgid "Toggle all object permissions"
msgstr ""

#. js-lingui-id: /kWolQ
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsTableHeader.tsx
msgid "Toggle all settings permissions"
msgstr ""

#. js-lingui-id: BRMXj0
#: src/modules/localization/utils/formatDateISOStringToRelativeDate.ts
msgid "Tomorrow"
msgstr ""

#. js-lingui-id: xdA/+p
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
msgid "Tools"
msgstr ""

#. js-lingui-id: vb0Q0/
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#~ msgid "Total Users"
#~ msgstr "Total Users"

#. js-lingui-id: dshGKq
#: src/modules/billing/components/SettingsBillingMonthlyCreditsSection.tsx
msgid "Track your monthly workflow credit consumption."
msgstr ""

#. js-lingui-id: wN0jHF
#: src/modules/information-banner/components/billing/InformationBannerBillingSubscriptionPaused.tsx
msgid "Trial expired. Please contact your admin"
msgstr ""

#. js-lingui-id: y985qL
#: src/modules/information-banner/components/billing/InformationBannerBillingSubscriptionPaused.tsx
msgid "Trial expired. Please update your billing details."
msgstr ""

#. js-lingui-id: PiUt3N
#: src/modules/command-menu/hooks/useWorkflowCommandMenu.ts
#: src/modules/command-menu/hooks/__tests__/useWorkflowCommandMenu.test.tsx
msgid "Trigger Type"
msgstr "트리거 유형"

#. js-lingui-id: IQ5pAL
#: src/modules/workflow/components/RecordShowPageWorkflowHeader.tsx
#~ msgid "Trigger type should be Manual - when no record(s) are selected"
#~ msgstr "Trigger type should be Manual - when no record(s) are selected"

#. js-lingui-id: c+xCSz
#: src/modules/settings/data-model/fields/forms/boolean/constants/BooleanDataModelSelectOptions.ts
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "True"
msgstr "참"

#. js-lingui-id: haaL9N
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
msgid "Try our REST or GraphQL API playgrounds."
msgstr "REST 또는 GraphQL API 플레이그라운드를 사용해 보십시오."

#. js-lingui-id: LQdi+H
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "Try with another email"
msgstr ""

#. js-lingui-id: Kz91g/
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Turkish"
msgstr "터키어"

#. js-lingui-id: +zy2Nq
#: src/pages/settings/data-model/SettingsObjectIndexTable.tsx
#: src/pages/settings/data-model/constants/SettingsObjectTableMetadata.ts
#: src/modules/settings/security/components/SSO/SettingsSSOIdentitiesProvidersForm.tsx
msgid "Type"
msgstr "유형"

#. js-lingui-id: U83IeL
#: src/modules/command-menu/components/CommandMenuTopBar.tsx
msgid "Type anything"
msgstr "아무거나 입력하세요"

#. js-lingui-id: V9+2pH
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Ukrainian"
msgstr "우크라이나어"

#. js-lingui-id: wSXm5S
#: src/pages/settings/data-model/SettingsObjectIndexTable.tsx
#: src/modules/object-record/record-board/record-board-column/utils/getAggregateOperationShortLabel.ts
msgid "Unique"
msgstr "고유"

#. js-lingui-id: Ef7StM
#: src/modules/settings/admin-panel/components/SettingsAdminVersionContainer.tsx
msgid "Unknown"
msgstr ""

#. js-lingui-id: 29VNqC
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Unknown error"
msgstr "알 수 없는 오류"

#. js-lingui-id: GQCXQS
#: src/pages/onboarding/ChooseYourPlan.tsx
#: src/pages/onboarding/ChooseYourPlan.tsx
msgid "Unlimited contacts"
msgstr "무제한 연락처"

#. js-lingui-id: xzOKup
#: src/modules/settings/roles/role/components/SettingsRoleCreateEffect.tsx
msgid "Untitled role"
msgstr ""

#. js-lingui-id: KmUc2w
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettings.tsx
#: src/modules/settings/roles/role/components/SettingsRoleLabelContainer.tsx
#: src/modules/settings/roles/role/components/SettingsRole.tsx
#~ msgid "Untitled Role"
#~ msgstr ""

#. js-lingui-id: Y8zko3
#: src/modules/settings/roles/role-permissions/object-level-permissions/utils/objectPermissionKeyToHumanReadableText.ts
msgid "update"
msgstr ""

#. js-lingui-id: EkH9pt
#: src/modules/information-banner/components/billing/InformationBannerFailPaymentInfo.tsx
#: src/modules/information-banner/components/billing/InformationBannerBillingSubscriptionPaused.tsx
msgid "Update"
msgstr ""

#. js-lingui-id: Vmsh9w
#: src/modules/settings/accounts/components/SetttingsAccountsImapConnectionForm.tsx
msgid "Update your IMAP email account configuration"
msgstr ""

#. js-lingui-id: +b7T3G
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Updated"
#~ msgstr "업데이트됨"

#. js-lingui-id: ONWvwQ
#: src/modules/ui/input/components/ImageInput.tsx
msgid "Upload"
msgstr "업로드"

#. js-lingui-id: 7OP1Xi
#: src/modules/spreadsheet-import/steps/components/UploadStep/components/DropZone.tsx
msgid "Upload .xlsx, .xls or .csv file"
msgstr ""

#. js-lingui-id: 2IXDgU
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Upload file"
msgstr "파일 업로드"

#. js-lingui-id: IQ3gAw
#: src/modules/spreadsheet-import/steps/components/SpreadsheetImportStepperContainer.tsx
msgid "Upload File"
msgstr ""

#. js-lingui-id: akDOEO
#: src/modules/settings/security/components/SSO/SettingsSSOSAMLForm.tsx
msgid "Upload the XML file with your connection infos"
msgstr "연결 정보가 포함된 XML 파일을 업로드하세요"

#. js-lingui-id: IagCbF
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "URL"
#~ msgstr "URL"

#. js-lingui-id: 6dMpmz
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
#: src/modules/action-menu/actions/record-actions/constants/WorkflowVersionsActionsConfig.tsx
msgid "Use as draft"
msgstr "초안으로 사용"

#. js-lingui-id: oTTQsc
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "Use letter, number and dash only. Start and finish with a letter or a number"
msgstr "문자, 숫자, 대시만 사용하세요. 문자 또는 숫자로 시작하고 끝내세요"

#. js-lingui-id: c6uZUV
#: src/modules/object-record/record-table/empty-state/utils/getEmptyStateSubTitle.ts
msgid "Use our API or add your first {objectLabel} manually"
msgstr "API를 사용하거나 첫 번째 {objectLabel}을 수동으로 추가하세요"

#. js-lingui-id: 7PzzBU
#: src/pages/settings/SettingsProfile.tsx
#: src/pages/settings/profile/appearance/components/SettingsExperience.tsx
#: src/pages/settings/accounts/SettingsAccountsEmails.tsx
#: src/pages/settings/accounts/SettingsAccountsCalendars.tsx
#: src/pages/settings/accounts/SettingsAccounts.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
msgid "User"
msgstr "사용자"

#. js-lingui-id: YFciqL
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#~ msgid "User Email"
#~ msgstr "User Email"

#. js-lingui-id: GjhOGB
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#~ msgid "User ID"
#~ msgstr "User ID"

#. js-lingui-id: RNv3YS
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "User Impersonation"
msgstr "사용자 대행"

#. js-lingui-id: tNT8wT
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "User Info"
msgstr "사용자 정보"

#. js-lingui-id: IjyOjp
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
#: src/modules/settings/security/components/SettingsSecurityAuthProvidersOptionsList.tsx
msgid "User is not logged in"
msgstr "사용자가 로그인하지 않았습니다"

#. js-lingui-id: 5ZYU8G
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#~ msgid "User Name"
#~ msgstr "User Name"

#. js-lingui-id: Sxm8rQ
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Users"
msgstr "Users"

#. js-lingui-id: vUr1/5
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "Using default application value. Configure via environment variables."
msgstr ""

#. js-lingui-id: 8Drj7i
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "Using default value. Set a custom value to override."
msgstr ""

#. js-lingui-id: MGeZsN
#: src/modules/spreadsheet-import/steps/components/SpreadsheetImportStepperContainer.tsx
#~ msgid "Validate data"
#~ msgstr ""

#. js-lingui-id: Clr4qp
#: src/modules/spreadsheet-import/steps/components/SpreadsheetImportStepperContainer.tsx
msgid "Validate Data"
msgstr ""

#. js-lingui-id: wMHvYH
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableValueInput.tsx
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableValueInput.tsx
msgid "Value"
msgstr ""

#. js-lingui-id: UZHvVi
#: src/modules/settings/admin-panel/config-variables/components/ConfigVariableHelpText.tsx
msgid "Value is set in the server environment, it may be a different value on the worker."
msgstr ""

#. js-lingui-id: fXVIZq
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
msgid "Values"
msgstr "값"

#. js-lingui-id: rawKOG
#: src/modules/settings/admin-panel/config-variables/hooks/useConfigVariableActions.ts
msgid "Variable deleted successfully."
msgstr ""

#. js-lingui-id: dY/1ir
#: src/modules/object-record/record-field/form-types/components/VariableChip.tsx
msgid "Variable not found"
msgstr "변수를 찾을 수 없음"

#. js-lingui-id: N/89ho
#: src/modules/settings/admin-panel/config-variables/hooks/useConfigVariableActions.ts
#~ msgid "Variable updated successfully"
#~ msgstr ""

#. js-lingui-id: 2ushgC
#: src/modules/settings/admin-panel/config-variables/hooks/useConfigVariableActions.ts
msgid "Variable updated successfully."
msgstr ""

#. js-lingui-id: IHIWR4
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Version of the application"
msgstr "응용 프로그램 버전"

#. js-lingui-id: fROFIL
#: src/pages/settings/profile/appearance/components/LocalePicker.tsx
msgid "Vietnamese"
msgstr "베트남어"

#. js-lingui-id: jpctdh
#: src/modules/settings/data-model/object-details/components/SettingsObjectFieldDisabledActionDropdown.tsx
msgid "View"
msgstr "보기"

#. js-lingui-id: KANz0G
#: src/pages/settings/SettingsBilling.tsx
msgid "View billing details"
msgstr "청구 세부 정보 보기"

#. js-lingui-id: igR+P/
#: src/modules/workflow/hooks/useRunWorkflowVersion.tsx
#~ msgid "View execution details"
#~ msgstr "실행 세부 사항 보기"

#. js-lingui-id: qZmd6a
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownViewSettingsContent.tsx
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownMenuContent.tsx
#~ msgid "View settings"
#~ msgstr "View settings"

#. js-lingui-id: bJAIqT
#: src/modules/views/view-picker/components/ViewPickerContentCreateMode.tsx
msgid "View type"
msgstr "보기 유형"

#. js-lingui-id: 2q/Q7x
#: src/modules/settings/accounts/components/SettingsAccountsMessageChannelDetails.tsx
msgid "Visibility"
msgstr "가시성"

#. js-lingui-id: oh8+os
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownFieldsContent.tsx
msgid "Visible"
msgstr "보임"

#. js-lingui-id: zYTdqe
#: src/modules/views/components/ViewBarFilterDropdownFieldSelectMenu.tsx
#: src/modules/object-record/object-sort-dropdown/components/ObjectSortDropdownButton.tsx
#: src/modules/object-record/advanced-filter/components/AdvancedFilterFieldSelectMenu.tsx
msgid "Visible fields"
msgstr ""

#. js-lingui-id: JiDDG4
#: src/modules/object-record/object-options-dropdown/components/ObjectOptionsDropdownRecordGroupsContent.tsx
msgid "Visible groups"
msgstr "표시 그룹"

#. js-lingui-id: 6n7jtr
#: src/modules/settings/data-model/objects/components/SettingsObjectCoverImage.tsx
msgid "Visualize"
msgstr "시각화"

#. js-lingui-id: zFSQY3
#: src/modules/activities/timeline-activities/rows/main-object/components/EventRowMainObject.tsx
msgid "was created by"
msgstr ""

#. js-lingui-id: uHpu1c
#: src/modules/activities/timeline-activities/rows/main-object/components/EventRowMainObject.tsx
msgid "was deleted by"
msgstr ""

#. js-lingui-id: S0enzB
#: src/modules/activities/timeline-activities/rows/main-object/components/EventRowMainObject.tsx
msgid "was restored by"
msgstr ""

#. js-lingui-id: 6eMAkI
#: src/modules/auth/sign-in-up/components/EmailVerificationSent.tsx
msgid "We encountered an issue verifying"
msgstr ""

#. js-lingui-id: id6ein
#: src/modules/ui/input/components/ImageInput.tsx
msgid "We support your square PNGs, JPEGs and GIFs under 10MB"
msgstr "10MB 미만의 정사각형 PNG, JPEG 및 GIF를 지원합니다"

#. js-lingui-id: ZS7vYp
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "We will send POST requests to this endpoint for every new event"
msgstr "모든 새로운 이벤트에 대해 이 엔드포인트로 POST 요청을 보냅니다"

#. js-lingui-id: PI4LiB
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
msgid "We will send your a link to verify domain ownership"
msgstr "도메인 소유권을 확인하기 위해 링크를 보내드립니다"

#. js-lingui-id: TRDppN
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Webhook"
#~ msgstr "Webhook"

#. js-lingui-id: Tvdycs
#: src/modules/settings/developers/hooks/useWebhookForm.ts
msgid "Webhook {targetUrl} created successfully"
msgstr ""

#. js-lingui-id: otr01y
#: src/modules/settings/developers/hooks/useWebhookForm.ts
msgid "Webhook {targetUrl} updated successfully"
msgstr ""

#. js-lingui-id: 2X4ecw
#: src/modules/settings/developers/hooks/useWebhookForm.ts
msgid "Webhook deleted successfully"
msgstr ""

#. js-lingui-id: 8bfORM
#: src/modules/settings/developers/hooks/useWebhookForm.ts
msgid "Webhook ID is required for deletion"
msgstr ""

#. js-lingui-id: +Fb+7w
#: src/modules/settings/developers/hooks/useWebhookForm.ts
msgid "Webhook ID is required for updates"
msgstr ""

#. js-lingui-id: v1kQyJ
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "Webhooks"
msgstr "Webhooks"

#. js-lingui-id: Jt1WNL
#: src/pages/auth/SignInUp.tsx
#~ msgid "Welcome to"
#~ msgstr "Welcome to"

#. js-lingui-id: ke0EDP
#: src/pages/auth/SignInUp.tsx
msgid "Welcome to {workspaceName}"
msgstr "{workspaceName}에 오신 것을 환영합니다"

#. js-lingui-id: C51ilI
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
msgid "When the API key will expire."
msgstr "API 키 만료일."

#. js-lingui-id: leUubq
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
msgid "When the key will be disabled"
msgstr "키 비활성화 시기"

#. js-lingui-id: RfrIUU
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
#~ msgid "Will be saved as:"
#~ msgstr ""

#. js-lingui-id: e9sagb
#: src/modules/workflow/workflow-trigger/components/WorkflowEditTriggerManualForm.tsx
msgid "Will return one {objectType} to the next step of this workflow"
msgstr ""

#. js-lingui-id: wvyffT
#: src/modules/workflow/components/RecordShowPageWorkflowHeader.tsx
#~ msgid "Workflow cannot be tested"
#~ msgstr "Workflow cannot be tested"

#. js-lingui-id: GpJjC8
#: src/modules/workflow/hooks/useRunWorkflowVersion.tsx
#~ msgid "Workflow is running..."
#~ msgstr "워크플로우가 실행 중입니다..."

#. js-lingui-id: o0xBLi
#: src/modules/workflow/hooks/useRunWorkflowVersion.tsx
#~ msgid "Workflow run failed"
#~ msgstr "워크플로우 실행 실패"

#. js-lingui-id: woYYQq
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
msgid "Workflows"
msgstr ""

#. js-lingui-id: pmUArF
#: src/pages/settings/SettingsWorkspaceMembers.tsx
#: src/pages/settings/SettingsWorkspace.tsx
#: src/pages/settings/SettingsBilling.tsx
#: src/pages/settings/workspace/SettingsDomain.tsx
#: src/pages/settings/security/SettingsSecuritySSOIdentifyProvider.tsx
#: src/pages/settings/security/SettingsSecurityApprovedAccessDomain.tsx
#: src/pages/settings/security/SettingsSecurity.tsx
#: src/pages/settings/integrations/SettingsIntegrations.tsx
#: src/pages/settings/developers/webhooks/components/SettingsWebhooks.tsx
#: src/pages/settings/developers/playground/SettingsRestPlayground.tsx
#: src/pages/settings/developers/playground/SettingsGraphQLPlayground.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeysNew.tsx
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/pages/settings/developers/api-keys/SettingsApiKeys.tsx
#: src/pages/settings/data-model/SettingsObjects.tsx
#: src/pages/settings/data-model/SettingsObjectFieldEdit.tsx
#: src/pages/settings/data-model/SettingsObjectDetailPage.tsx
#: src/pages/settings/data-model/SettingsNewObject.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldSelect.tsx
#: src/pages/settings/data-model/SettingsObjectNewField/SettingsObjectNewFieldConfigure.tsx
#: src/modules/sign-in-background-mock/components/SignInAppNavigationDrawerMock.tsx
#: src/modules/settings/roles/role-permissions/settings-permissions/components/SettingsRolePermissionsSettingsSection.tsx
#: src/modules/settings/roles/role-permissions/object-level-permissions/object-form/components/SettingsRolePermissionsObjectLevelObjectForm.tsx
#: src/modules/settings/roles/components/SettingsRolesContainer.tsx
#: src/modules/settings/hooks/useSettingsNavigationItems.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
#: src/modules/favorites/components/WorkspaceFavorites.tsx
msgid "Workspace"
msgstr "워크스페이스"

#. js-lingui-id: VicISP
#: src/modules/settings/profile/components/DeleteWorkspace.tsx
msgid "Workspace Deletion"
msgstr "워크스페이스 삭제"

#. js-lingui-id: J22jAC
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
msgid "Workspace Info"
msgstr "워크스페이스 정보"

#. js-lingui-id: Y0Fj4S
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Workspace logo"
msgstr "워크스페이스 로고"

#. js-lingui-id: CozWO1
#: src/pages/onboarding/CreateWorkspace.tsx
msgid "Workspace name"
msgstr "워크스페이스 이름"

#. js-lingui-id: 6X+cfX
#: src/modules/settings/admin-panel/components/SettingsAdminWorkspaceContent.tsx
#~ msgid "Workspace Name"
#~ msgstr "Workspace Name"

#. js-lingui-id: pmt7u4
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
msgid "Workspaces"
msgstr "워크스페이스"

#. js-lingui-id: 5iSD9O
#: src/modules/settings/data-model/fields/forms/components/text/SettingsDataModelFieldTextForm.tsx
msgid "Wrap on record pages"
msgstr "레코드 페이지에 래핑"

#. js-lingui-id: Q9pNST
#: src/modules/settings/roles/role-settings/components/SettingsRoleSettings.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
#: src/modules/settings/data-model/objects/forms/components/SettingsDataModelObjectAboutForm.tsx
#: src/modules/settings/data-model/fields/forms/components/SettingsDataModelFieldDescriptionForm.tsx
msgid "Write a description"
msgstr "설명을 작성하세요"

#. js-lingui-id: L80fMJ
#: src/pages/settings/developers/webhooks/components/SettingsDevelopersWebhookDetail.tsx
#~ msgid "Write a secret"
#~ msgstr "비밀 작성"

#. js-lingui-id: zkWmBh
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "Yearly"
msgstr ""

#. js-lingui-id: 3d1wCB
#: src/pages/settings/developers/api-keys/SettingsDevelopersApiKeyDetail.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
#: src/modules/settings/developers/components/SettingsDevelopersWebhookForm.tsx
msgid "yes"
msgstr "예"

#. js-lingui-id: y/0uwd
#: src/modules/localization/utils/formatDateISOStringToRelativeDate.ts
msgid "Yesterday"
msgstr ""

#. js-lingui-id: lWUeEt
#: src/modules/auth/sign-in-up/hooks/useWorkspaceFromInviteHash.ts
msgid "You already belong to the workspace {workspaceDisplayName}"
msgstr ""

#. js-lingui-id: Q1CQiy
#: src/modules/auth/sign-in-up/hooks/useWorkspaceFromInviteHash.ts
msgid "You already belong to this workspace"
msgstr ""

#. js-lingui-id: qsAug0
#: src/modules/spreadsheet-import/components/MatchColumnSelect.tsx
#~ msgid "You are already importing this column."
#~ msgstr ""

#. js-lingui-id: MFDARJ
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateReadOnly.tsx
msgid "You are not allowed to create records for this object"
msgstr "이 개체에 기록을 생성할 수 있는 권한이 없습니다"

#. js-lingui-id: 5eVYbs
#: src/modules/object-record/record-table/empty-state/components/RecordTableEmptyStateReadOnly.tsx
#~ msgid "You are not allowed to create records in this object"
#~ msgstr "You are not allowed to create records in this object"

#. js-lingui-id: TBApzn
#: src/modules/settings/admin-panel/components/SettingsAdminGeneral.tsx
#~ msgid "You do not have access to impersonate users."
#~ msgstr "You do not have access to impersonate users."

#. js-lingui-id: cdk3eC
#: src/modules/workflow/workflow-steps/workflow-actions/ai-agent-action/components/WorkflowEditActionAiAgent.tsx
msgid "You haven't configured any model provider. Please set up an API Key in your instance's admin panel or as an environment variable."
msgstr ""

#. js-lingui-id: zEM7Ne
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "You will be charged ${enterprisePrice} per user per month billed annually."
msgstr ""

#. js-lingui-id: HM4D8s
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "You will be charged ${enterprisePrice} per user per month."
msgstr ""

#. js-lingui-id: o4xIH4
#: src/modules/billing/components/SettingsBillingSubscriptionInfo.tsx
msgid "You will be charged ${yearlyPrice} per user per month billed annually. A prorata with your current subscription will be applied."
msgstr ""

#. js-lingui-id: zSkMV0
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "You will be charged immediately for the full year."
#~ msgstr "1년 요금이 즉시 청구됩니다."

#. js-lingui-id: C2d+Xk
#: src/modules/spreadsheet-import/steps/components/MatchColumnsStep/MatchColumnsStep.tsx
msgid "You will lose all your mappings."
msgstr ""

#. js-lingui-id: Cl+hUj
#: src/pages/settings/workspace/SettingsDomain.tsx
msgid "You're about to change your workspace subdomain. This action will log out all users."
msgstr ""

#. js-lingui-id: XVnj6K
#: src/pages/settings/SettingsBilling.tsx
#~ msgid "Your credit balance will be used to pay the monthly bills."
#~ msgstr "크레딧 잔액이 월 청구서 결제에 사용됩니다."

#. js-lingui-id: +5YqGH
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Your email subjects and meeting titles will be shared with your team."
msgstr "이메일 제목과 회의 제목이 팀과 공유됩니다."

#. js-lingui-id: la0RPA
#: src/modules/onboarding/components/onboardingSyncEmailsOptions.tsx
msgid "Your emails and events content will be shared with your team."
msgstr "이메일과 이벤트 내용이 팀과 공유됩니다."

#. js-lingui-id: 9ivpwk
#: src/pages/settings/SettingsProfile.tsx
msgid "Your name as it will be displayed"
msgstr "표시될 이름"

#. js-lingui-id: 3RASGN
#: src/pages/onboarding/CreateProfile.tsx
msgid "Your name as it will be displayed on the app"
msgstr "앱에 디스플레이될 귀하의 이름"

#. js-lingui-id: YQK8fJ
#: src/pages/auth/SignInUp.tsx
msgid "Your Workspace"
msgstr "귀하의 워크스페이스"

#. js-lingui-id: oC6WBs
#: src/modules/information-banner/components/billing/InformationBannerNoBillingSubscription.tsx
msgid "Your workspace doesn't have an active subscription."
msgstr ""

#. js-lingui-id: QBTEtW
#: src/modules/information-banner/components/billing/InformationBannerNoBillingSubscription.tsx
msgid "Your workspace doesn't have an active subscription. Please contact your admin."
msgstr ""

#. js-lingui-id: RhNbPE
#: src/pages/settings/SettingsBilling.tsx
msgid "Your workspace will be disabled"
msgstr "워크스페이스가 비활성화됩니다"
