/*eslint-disable*/import type{Messages}from"@lingui/core";export const messages=JSON.parse("{\"/8h/VA\":[\" must be one of \",[\"ratingValues\"],\" values\"],\"gdf0h7\":[\" These are only the server values. Ensure your worker environment has the\\n        same variables and values, this is required for asynchronous tasks like\\n        email sync.\"],\"ypz2+E\":[\": Empty\"],\"CE75IR\":[\": Future\"],\"Nk/d+Z\":[\": Not\"],\"1ORnec\":[\": NotEmpty\"],\"11QpPG\":[\": NotNull\"],\"gK4ysT\":[\": Past\"],\"4iJDkt\":[\": Today\"],\"nSri0b\":[\"· \",[\"fieldSearchInputValue\"]],\"ZBGbuw\":[\"[empty string]\"],\"J/hVSQ\":[[\"0\"]],\"ROdDR9\":[[\"aggregateLabel\"],\" of \",[\"fieldLabel\"]],\"z5EvY5\":[[\"apiKeyName\"]],\"uogEAL\":[[\"apiKeyName\"],\" API Key\"],\"zi5SHH\":[[\"contextStoreNumberOfSelectedRecords\"],\" selected\"],\"WS/avX\":[[\"formattedCreatedRecordsProgress\"],\" out of \",[\"formattedRecordsToImportCount\"],\" records imported.\"],\"wapGcj\":[[\"message\"]],\"6j5rE1\":[[\"name\"]],\"OeIgEg\":[[\"roleLabel\"],\" can \",[\"humanReadableAction\"],\" \",[\"objectLabel\"],\" records\"],\"D0C09b\":[[\"roleLabel\"],\" can't \",[\"humanReadableAction\"],\" \",[\"objectLabel\"],\" records\"],\"pDgeaz\":[[\"title\"]],\"WN9tFl\":[[\"workspaceMemberName\"],\" will be unassigned from the following role:\"],\"CwstSL\":[\"••••••••\"],\"NNQszp\":[\"⚠️ Please verify the auto mapping of the columns. You can also ignore or change the mapping of the columns.\"],\"YT0WJ4\":[\"1 000 workflow node executions\"],\"vb5TwV\":[\"1. Select a field type\"],\"XSdCGq\":[\"1. Select an object\"],\"SLjiTq\":[\"1. Type\"],\"AvXug3\":[\"10 000 workflow node executions\"],\"7IIx+y\":[\"10,000 workflow node executions\"],\"4EdXYs\":[\"12h (\",[\"hour12Label\"],\")\"],\"yXvRMf\":[\"2. Configure\"],\"0HAF12\":[\"2. Configure field\"],\"0Tx9MD\":[\"2. Set \",[\"objectLabelPlural\"],\" permissions\"],\"kAtmAv\":[\"20 000 workflow node executions\"],\"YfwnsU\":[\"20,000 workflow node executions\"],\"QsMprd\":[\"24h (\",[\"hour24Label\"],\")\"],\"IbMa7Z\":[\"993\"],\"nMTB1f\":[\"A shared environment where you will be able to manage your customer relations with your team.\"],\"/cSHSG\":[\"A verification email has been sent to\"],\"JE9zuL\":[\"Ability to edit all settings\"],\"09tRFp\":[\"Ability to interact with each object\"],\"4lzAkF\":[\"Ability to interact with specific objects\"],\"nJHSWx\":[\"Ability to interact with this specific object\"],\"ssjjFt\":[\"Abort\"],\"uyJsf6\":[\"About\"],\"NBHoKd\":[\"About my subscription\"],\"DhgC7B\":[\"About this user\"],\"PtK3Kn\":[\"About this workspace\"],\"AeXO77\":[\"Account\"],\"nD0Y+a\":[\"Account Deletion\"],\"bPwFdf\":[\"Accounts\"],\"GGcxJO\":[\"ACS Url copied to clipboard\"],\"bwRvnp\":[\"Action\"],\"7L01XJ\":[\"Actions\"],\"9MxZtA\":[\"Actions users can perform on all objects\"],\"xnXGwA\":[\"Actions users can perform on specific objects\"],\"N4ZcCd\":[\"Actions users can perform on this object\"],\"Ws0qQB\":[\"Actions you can perform on all objects\"],\"FQBaXG\":[\"Activate\"],\"tu8A/k\":[\"Activate Workflow\"],\"F6pfE9\":[\"Active\"],\"Mue4oc\":[\"Active API keys created by you or your team.\"],\"m16xKo\":[\"Add\"],\"MPPZ54\":[\"Add account\"],\"HD0x5p\":[\"Add Approved Access Domain\"],\"DpV70M\":[\"Add Field\"],\"vCSBPD\":[\"Add filter\"],\"7XzAKI\":[\"Add inputs to your form\"],\"Hkobke\":[\"Add new\"],\"dEO3Zx\":[\"Add object\"],\"mFtRj8\":[\"Add object permission\"],\"Dl5lVI\":[\"Add option\"],\"M067Bn\":[\"Add or remove users\"],\"7YdkgZ\":[\"Add Output Field\"],\"O8tK4v\":[\"Add rule\"],\"sgXUv+\":[\"Add SSO Identity Provider\"],\"5+ttxv\":[\"Add to blocklist\"],\"yVOmgE\":[\"Add to Favorite\"],\"pBsoKL\":[\"Add to favorites\"],\"q9e2Bs\":[\"Add view\"],\"m2qDV8\":[\"Add your first \",[\"objectLabel\"]],\"vLO+NG\":[\"Added \",[\"beautifiedCreatedAt\"]],\"jEHeq+\":[\"Added \",[\"beautifyPastDateRelative\"]],\"9iIYOy\":[\"Added to blocklist\"],\"Eis4ey\":[\"Adjust the role-related settings\"],\"g1in8j\":[\"Admin Panel\"],\"ae6Tab\":[\"Admin settings and system tools\"],\"sxkWRg\":[\"Advanced\"],\"tMFFwF\":[\"Advanced filter\"],\"WsKDNF\":[\"Advanced:\"],\"1Cox/a\":[\"Afrikaans\"],\"MnmJG1\":[\"AI Agent\"],\"kNfr85\":[\"AI Model\"],\"QwgquD\":[\"AI Response Schema\"],\"N40H+G\":[\"All\"],\"3saA7W\":[\"All (\",[\"relationRecordsCount\"],\")\"],\"9ljU00\":[\"All Actions\"],\"7EZqN0\":[\"All emails and events linked to this account will be deleted\"],\"3xugyj\":[\"All fields\"],\"623MHa\":[\"All lines\"],\"zIk4M6\":[\"All objects\"],\"aFE/OW\":[\"All Objects\"],\"Hm90t3\":[\"All roles\"],\"F8bx9e\":[\"All the basic objects\"],\"XuuWVF\":[\"All the standard objects\"],\"MLmnB2\":[\"All workspaces this user is a member of\"],\"pPTmfc\":[\"All your custom objects\"],\"GMx1K0\":[\"Allow logins through Google's single sign-on functionality.\"],\"dea+zy\":[\"Allow logins through Microsoft's single sign-on functionality.\"],\"U6bnfj\":[\"Allow Support Team Access\"],\"wMg43c\":[\"Allow the invitation of new users by sharing an invite link.\"],\"vHeVg5\":[\"Allow users to sign in with an email and password.\"],\"LG4K0m\":[\"An error occurred while updating password\"],\"XyOToQ\":[\"An error occurred.\"],\"mJ6m4C\":[\"An optional description\"],\"lxentK\":[\"An unexpected error occurred\"],\"HZFm5R\":[\"and\"],\"xJR+Wq\":[\"Anyone with an email address at these domains is allowed to sign up for this workspace.\"],\"OZtEcz\":[\"API\"],\"0RqpZr\":[\"API & Webhooks\"],\"yRnk5W\":[\"API Key\"],\"r+NRG6\":[\"API Key copied to clipboard\"],\"5h8ooz\":[\"API keys\"],\"vByqA1\":[\"API Keys & Webhooks\"],\"kAtj+q\":[\"API Name\"],\"lwCAhN\":[\"API Name (Plural)\"],\"KclpRp\":[\"API Name (Singular)\"],\"Z3Brb2\":[\"API values\"],\"JR6nY7\":[\"APIs\"],\"aAIQg2\":[\"Appearance\"],\"UDlRcx\":[\"Approved access domain validated\"],\"9tggYj\":[\"Approved Domains\"],\"1844JP\":[\"Approved Email Domain\"],\"8HV3WN\":[\"Arabic\"],\"3iX0kh\":[\"Are you sure that you want to change your billing interval?\"],\"CWb74a\":[\"Are you sure that you want to change your billing interval? You will be charged immediately for the full year.\"],\"3SRf5B\":[\"Are you sure you want to assign this role?\"],\"8Y3Jbl\":[\"Are you sure you want to delete these records? They can be recovered from the Command menu (\",[\"osControlSymbol\"],\" + K).\"],\"2BZAqa\":[\"Are you sure you want to delete these records? They can be recovered from the Command menu.\"],\"Se0vJw\":[\"Are you sure you want to delete this record? It can be recovered from the Command menu (\",[\"osControlSymbol\"],\" + K).\"],\"yb2hF4\":[\"Are you sure you want to delete this record? It can be recovered from the Command menu.\"],\"rnCqBK\":[\"Are you sure? Your current information will not be saved.\"],\"nYD/Cq\":[\"Ascending\"],\"9ch9Mz\":[\"Assign \",[\"workspaceMemberName\"],\"?\"],\"449JVv\":[\"Assign Role\"],\"17M/rH\":[\"Assign roles to manage each member’s access and permissions\"],\"rfYmIr\":[\"Assign roles to specify each member's access permissions\"],\"2y2quh\":[\"Assign to member\"],\"OItM/o\":[\"Assigned members\"],\"lxQ+5m\":[\"Assigned to\"],\"0dtKl9\":[\"Assignment\"],\"H8QGSx\":[\"At least 8 characters long.\"],\"Y7Dx6e\":[\"At least one authentication method must be enabled\"],\"P8fBlG\":[\"Authentication\"],\"htuqBH\":[\"Authentication failed\"],\"yIVrHZ\":[\"Authorize\"],\"wTBNbL\":[\"Authorized URI\"],\"Ovw0c6\":[\"Authorized URL copied to clipboard\"],\"2zJkmL\":[\"Auto-creation\"],\"YRT7ZW\":[\"Automatically create contacts for people you've participated in an event with.\"],\"lgw3U4\":[\"Automatically create contacts for people.\"],\"RpExX0\":[\"Automatically create People records when receiving or sending emails\"],\"csDS2L\":[\"Available\"],\"3uQmjD\":[\"Average\"],\"iH8pgl\":[\"Back\"],\"Dht9W3\":[\"Back to content\"],\"ehOkF+\":[\"Basics\"],\"R+w/Va\":[\"Billing\"],\"nJGwRf\":[\"Billing interval\"],\"K1172m\":[\"Blocklist\"],\"2yl5lQ\":[\"Book a Call\"],\"d/yr35\":[\"Book onboarding\"],\"2X0NCp\":[\"Book your onboarding\"],\"bOwPjT\":[\"Boolean\"],\"qDsMss\":[\"Brief explanation of this output field\"],\"8Pfllj\":[\"By using Twenty, you agree to the\"],\"PmmvzS\":[\"Calculate\"],\"AjVXBS\":[\"Calendar\"],\"wRpDAv\":[\"Calendar Event\"],\"wLtx+m\":[\"Calendar settings\"],\"LbIh3Y\":[\"Calendar Sync\"],\"HlJKLT\":[\"Calendar Sync Status\"],\"EUpfsd\":[\"Calendars\"],\"msssZq\":[\"Can't change API names for standard objects\"],\"dEgA5A\":[\"Cancel\"],\"0TllC8\":[\"Cancel anytime\"],\"rRK/Lf\":[\"Cancel Plan\"],\"N6gPiD\":[\"Cancel your subscription\"],\"M1RLfx\":[\"Catalan\"],\"OfzMnb\":[\"Change \",[\"to\"]],\"feCRNv\":[\"Change email\"],\"VhMDMg\":[\"Change Password\"],\"wRtBJP\":[\"Change Plan\"],\"EYSFEW\":[\"Change subdomain?\"],\"C8G9Mq\":[\"Change to Organization Plan?\"],\"b0h4XW\":[\"Change to yearly\"],\"N8mUQE\":[\"Change to Yearly?\"],\"AwAGI4\":[\"Check your Emails\"],\"SxwQE8\":[\"Checkout session error. Please retry or contact Twenty team\"],\"SviKkE\":[\"Chinese — Simplified\"],\"dzb4Ep\":[\"Chinese — Traditional\"],\"NFfUic\":[\"Choose a Workspace\"],\"JEFFOR\":[\"Choose an object\"],\"Qz73jD\":[\"Choose between OIDC and SAML protocols\"],\"2ZFG9X\":[\"Choose between Short and Full\"],\"YcrXB2\":[\"Choose the default currency that will apply\"],\"LHce7q\":[\"Choose the fields that will identify your records\"],\"hIJigY\":[\"Choose the format used to display date value\"],\"KT6rEB\":[\"Choose your provider\"],\"9qP96p\":[\"Choose your Trial\"],\"xCJdfg\":[\"Clear\"],\"wu0RfR\":[\"Clear the field or \\\"X\\\" to revert to environment/default value.\"],\"HevGC0\":[\"Click on \\\"Add Field\\\" below to add the first input to your form. The form will pop up on the user's screen when the workflow is launched from a manual trigger. For other types of triggers, it will be displayed in the Workflow run record page.\"],\"RFmRvm\":[\"Click on \\\"Add Output Field\\\" below to define the structure of your AI agent's response. These fields will be used to format and validate the AI's output when the workflow is executed, and can be referenced by subsequent workflow steps.\"],\"b8wwse\":[\"Click on the checkmark to apply your changes.\"],\"b9Y4up\":[\"Client ID\"],\"Bdj4LI\":[\"Client Secret\"],\"XUe4cu\":[\"Client Settings\"],\"yz7wBu\":[\"Close\"],\"qYsAlX\":[\"Close command menu\"],\"EWPtMO\":[\"Code\"],\"H86f9p\":[\"Collapse\"],\"Xose0w\":[\"Color code\"],\"41xV/k\":[\"column data is not compatible with Multi-Select.\"],\"93hd3e\":[\"Columns not matched:\"],\"Icd+JO\":[\"Command menu icon\"],\"NM9bMd\":[\"Compact view\"],\"s2QZS6\":[\"Companies\"],\"0CuAor\":[\"Conditions\"],\"Vj43Y/\":[\"Config Variables\"],\"+zUMwJ\":[\"Configure an SSO connection\"],\"QTNsSm\":[\"Configure and customize your calendar preferences.\"],\"aGwm+D\":[\"Configure how dates are displayed across the app\"],\"ghdb7+\":[\"Configure how we should display your events in your calendar\"],\"Bh4GBD\":[\"Configure your emails and calendar settings.\"],\"W4D3v/\":[\"Configure your IMAP email account\"],\"7VpPHA\":[\"Confirm\"],\"FbJ7Si\":[\"Confirm deletion of \",[\"roleName\"],\" role? This cannot be undone. All members will be reassigned to the default role.\"],\"D8ATlr\":[\"Connect a new account to your workspace\"],\"Zgi9Fd\":[\"Connect with Google\"],\"VZvZWq\":[\"Connect with IMAP\"],\"IOfqM8\":[\"Connect with Microsoft\"],\"9TzudL\":[\"Connected accounts\"],\"Y2y0mC\":[\"Contact auto-creation\"],\"/5mghO\":[\"Contains\"],\"M73whl\":[\"Context\"],\"xGVfLh\":[\"Continue\"],\"RvVi9c\":[\"Continue with Email\"],\"oZyG4C\":[\"Continue with Google\"],\"ztoybH\":[\"Continue with Microsoft\"],\"FxVG/l\":[\"Copied to clipboard\"],\"u+VWhB\":[\"Copied to clipboard!\"],\"CcGOj+\":[\"Copilot\"],\"he3ygx\":[\"Copy\"],\"7eVkEH\":[\"Copy invitation link\"],\"y1eoq1\":[\"Copy link\"],\"eZ5HO9\":[\"Copy link to view\"],\"Ej5euX\":[\"Copy this key as it will not be visible again\"],\"SGsgDO\":[\"Core\"],\"w5Stxm\":[\"Correct the issues and fill the missing data.\"],\"I99Miw\":[\"Cost\"],\"iX2opZ\":[\"Cost per 1k Extra Credits\"],\"VVRHmR\":[\"Could not delete approved access domain\"],\"s8lFtq\":[\"Couldn't copy to clipboard\"],\"wBMjJ2\":[\"Count\"],\"EkZfen\":[\"Count all\"],\"vQJINq\":[\"Count empty\"],\"sLDQbp\":[\"Count false\"],\"DzRsDJ\":[\"Count not empty\"],\"ft6dHY\":[\"Count true\"],\"9FZBbf\":[\"Count unique values\"],\"hYgDIe\":[\"Create\"],\"zNoOC2\":[\"Create a workflow and return here to view its versions\"],\"Hn5erC\":[\"Create a workspace\"],\"uXGLuq\":[\"Create API key\"],\"d0DCww\":[\"Create new record\"],\"UDbEyl\":[\"Create new workflow\"],\"gSyzEV\":[\"Create profile\"],\"RoyYUE\":[\"Create Role\"],\"6MP9lc\":[\"Create select field\"],\"jmetDx\":[\"Create Select...\"],\"zt6Erc\":[\"Create view\"],\"dkAPxi\":[\"Create Webhook\"],\"kdLGki\":[\"Create Workspace\"],\"9chYz/\":[\"Create your workspace\"],\"d+F6q9\":[\"Created\"],\"fR16ki\":[\"Creating your data model...\"],\"TlL0Pl\":[\"Creating your schema\"],\"YO4SdK\":[\"Creating your workspace\"],\"R3PLzn\":[\"currencies\"],\"6YzXhC\":[\"Current value from server environment. Set a custom value to override.\"],\"Hd06oy\":[\"Current version\"],\"p76aoR\":[\"Current version:\"],\"8Tg/JR\":[\"Custom\"],\"XQ681Q\":[\"Custom Domain\"],\"1GTWIA\":[\"Custom domain updated\"],\"8skTDV\":[\"Custom objects\"],\"qt+EaC\":[\"Customise the fields available in the \",[\"objectLabelSingular\"],\" views.\"],\"CMhr4u\":[\"Customize Domain\"],\"RP2we8\":[\"Customize fields\"],\"bCJa9l\":[\"Customize your workspace security\"],\"w9VTXG\":[\"Czech\"],\"Zz6Cxn\":[\"Danger zone\"],\"Fo2vDn\":[\"Danish\"],\"pvnfJD\":[\"Dark\"],\"TtG/MN\":[\"Data deletion\"],\"5cNMFz\":[\"Data model\"],\"4BuYDo\":[\"Data Model\"],\"r+cVRP\":[\"Data type\"],\"GEMJqo\":[\"Database configuration is currently disabled.\"],\"mYGY3B\":[\"Date\"],\"Ud9zHv\":[\"Date and time\"],\"Lhd0oQ\":[\"Date format\"],\"5y3O+A\":[\"Deactivate\"],\"qk4i22\":[\"Deactivate \\\"Synchronize Objects Labels and API Names\\\" to set a custom API name\"],\"T2YbXF\":[\"Deactivate object\"],\"gexAq8\":[\"Deactivate this field\"],\"4tpC8V\":[\"Deactivate Workflow\"],\"yAT3be\":[\"Deactivated\"],\"ovBPCi\":[\"Default\"],\"mC21D6\":[\"Default Country\"],\"4zuPQL\":[\"Default Country Code\"],\"TlEEts\":[\"Default value\"],\"aQ8swY\":[\"Default Value\"],\"Y2ImVJ\":[\"Define the name and description of your object\"],\"Ntu10D\":[\"Define user roles and access levels\"],\"bQkkFU\":[\"Define what will be visible to other users in your workspace\"],\"HNlEFZ\":[\"delete\"],\"cnGeoo\":[\"Delete\"],\"2nRIIp\":[\"Delete \",[\"objectLabel\"]],\"ZDGm40\":[\"Delete account\"],\"gAz0S5\":[\"Delete account and all the associated data\"],\"hGfWDm\":[\"Delete API key\"],\"x2p4/4\":[\"Delete field\"],\"4dpwsE\":[\"Delete record\"],\"kf0A63\":[\"Delete records\"],\"0ptEoK\":[\"Delete Records on \",[\"objectLabel\"]],\"mcXHCJ\":[\"Delete Records on All Objects\"],\"efjzvW\":[\"Delete role\"],\"v41A6S\":[\"Delete Role Permanently\"],\"T6S2Ns\":[\"Delete this integration\"],\"c0vHQI\":[\"Delete this role and assign a new role to its members\"],\"KSOhjo\":[\"Delete this webhook\"],\"aRG49z\":[\"Delete view\"],\"snMaH4\":[\"Delete webhook\"],\"UA2IpC\":[\"Delete workflow\"],\"ABwG9x\":[\"Delete workflows\"],\"kYu0eF\":[\"Delete workspace\"],\"mk2Ygs\":[\"Delete your whole workspace\"],\"vGjmyl\":[\"Deleted\"],\"kcGoDz\":[\"Deleted \",[\"objectNamePlural\"]],\"WH/5rN\":[\"Deleted records\"],\"/TC7qI\":[\"Deleted runs\"],\"Wj5mzm\":[\"Deleted versions\"],\"gw3Tlm\":[\"Deleted workflows\"],\"Cko536\":[\"Descending\"],\"g+5exC\":[\"Describe what you want the AI to do...\"],\"Nu4oKW\":[\"Description\"],\"+ow7t4\":[\"destroy\"],\"2xxBws\":[\"Destroy\"],\"vI0W/N\":[\"Destroy \",[\"objectLabel\"]],\"fq4Wxy\":[\"Destroy Records on \",[\"objectLabel\"]],\"L68qSB\":[\"Destroy Records on All Objects\"],\"n+SX4g\":[\"Developers\"],\"zAg2B9\":[\"Discard Draft\"],\"Xm/s+u\":[\"Display\"],\"8Vg8H7\":[\"Display as a plain number or a percentage\"],\"i66xz9\":[\"Display as relative date\"],\"e/xgrw\":[\"Display Format\"],\"GoHpxA\":[\"Display text on multiple lines\"],\"ji7sqw\":[\"Do not import\"],\"U9q4zF\":[\"Doesn't contain\"],\"EoKe5U\":[\"Domain\"],\"yGpVSw\":[\"Domain added successfully.\"],\"7kVRe6\":[\"Domains have to be smaller than 256 characters, cannot contain spaces and cannot contain any special characters.\"],\"KUxVkp\":[\"Don’t auto-create contacts.\"],\"cx0Ws8\":[\"Don’t create contacts from/to Gmail, Outlook emails\"],\"3qDEYI\":[\"Don’t sync emails from team@ support@ noreply@...\"],\"WcWS//\":[\"Download file\"],\"wrT4hq\":[\"Download sample file.\"],\"6D/BnN\":[\"Drop file here...\"],\"KIjvtr\":[\"Dutch\"],\"QVVmxi\":[\"E.g. backoffice integration\"],\"GhTFTJ\":[\"e.g., summary, status, count\"],\"tOkc8o\":[\"Earliest\"],\"JTbQuO\":[\"Earliest date\"],\"ePK91l\":[\"Edit\"],\"fGxkii\":[\"Edit \",[\"objectLabel\"]],\"v+uKyy\":[\"Edit billing interval\"],\"6vZ75d\":[\"Edit CRM data structure and fields\"],\"9QCQIc\":[\"Edit field values\"],\"oKQ7ls\":[\"Edit Fields\"],\"8gm+y4\":[\"Edit IMAP Connection\"],\"h2KoTu\":[\"Edit payment method, see your invoices and more\"],\"MGkYcE\":[\"Edit Records on \",[\"objectLabel\"]],\"6n2zpV\":[\"Edit Records on All Objects\"],\"6o1M/Q\":[\"Edit your subdomain name or set a custom domain.\"],\"O3oNi5\":[\"Email\"],\"hzKQCy\":[\"Email Address\"],\"2SWjdJ\":[\"Email can not be empty\"],\"unEEog\":[\"Email cannot be empty\"],\"Zb5c/r\":[\"Email Connections\"],\"lfQsvW\":[\"Email integration\"],\"QT/Wo7\":[\"Email or domain is already in blocklist\"],\"LimKOG\":[\"Email verification\"],\"FiEQ3l\":[\"Email verification failed\"],\"hn6Eox\":[\"Email Verification Failed\"],\"TBv/iZ\":[\"Email verification failed.\"],\"svTijF\":[\"Email verification link resent!\"],\"00icDW\":[\"Email verified.\"],\"VI2hiF\":[\"Email/Domain\"],\"BXEcos\":[\"Emails\"],\"Ww/M6X\":[\"Emails settings\"],\"eXoH4Q\":[\"employees\"],\"gqv5ZL\":[\"Employees\"],\"N2S1rs\":[\"Empty\"],\"OMbipf\":[\"Empty Array\"],\"3hSGoJ\":[\"Empty Object\"],\"q1srUM\":[\"Encryption\"],\"T3juzf\":[\"Endpoint URL\"],\"lYGfRP\":[\"English\"],\"/bfFKe\":[\"Enjoy a \",[\"withCreditCardTrialPeriodDuration\"],\"-days free trial\"],\"oiVP/+\":[\"Enter a value to store in database\"],\"9/30HU\":[\"Enter in <0>Unicode</0> format\"],\"XJU8BD\":[\"Enter the credentials to set the connection\"],\"rGWgcm\":[\"Enter the infos to set the connection\"],\"rYIISB\":[\"Enter user ID or email address\"],\"GpB8YV\":[\"Enterprise\"],\"8PrrNJ\":[\"Entity ID copied to clipboard\"],\"7RnAWe\":[\"Env Variables\"],\"vPWtZ+\":[\"Environment variable\"],\"XOEl9R\":[\"Error copying to clipboard\"],\"c3qGJX\":[\"Error deleting api key: \",[\"err\"]],\"GHKxvg\":[\"Error deleting api key.\"],\"QnVLjD\":[\"Error deleting invitation\"],\"cyvTSq\":[\"Error deleting SSO Identity Provider\"],\"WEltn2\":[\"Error editing SSO Identity Provider\"],\"2GvilU\":[\"Error loading message\"],\"bj7nh3\":[\"Error regenerating api key: \",[\"err\"]],\"PfAip2\":[\"Error regenerating api key.\"],\"clfpgU\":[\"Error resending invitation\"],\"nB84mG\":[\"Error validating approved access domain\"],\"QmR+2U\":[\"Error while ending trial period. Please contact Twenty team.\"],\"VSQxWH\":[\"Error while switching subscription \",[\"to\"],\".\"],\"JKNROf\":[\"Error while switching subscription to Organization Plan.\"],\"k06M7e\":[\"Error while switching subscription to yearly.\"],\"ENV7jU\":[\"Error while switching subscription to Yearly.\"],\"JLxMta\":[\"Establish Webhook endpoints for notifications on asynchronous events.\"],\"poC90w\":[\"Event visibility\"],\"jvNRZW\":[\"Events you participated in are displayed in red.\"],\"wqF3jl\":[\"Everything\"],\"QQlMid\":[\"Exclude group emails\"],\"+tk2yZ\":[\"Exclude non-professional emails\"],\"cIgBjB\":[\"Exclude the following people and domains from my email sync\"],\"yhhfqh\":[\"Exclude the following people and domains from my email sync. Internal conversations will not be imported\"],\"fV7V51\":[\"Existing objects\"],\"ydzS9x\":[\"Exit\"],\"LFNXuj\":[\"Exit Full Screen\"],\"XB4onr\":[\"Exit import flow\"],\"IZ4o2e\":[\"Exit Settings\"],\"1A3EXy\":[\"Expand\"],\"tXGQvS\":[\"Expected selected node to be a create step node.\"],\"bKBhgb\":[\"Experience\"],\"LxRNPw\":[\"Expiration\"],\"SkXfL0\":[\"Expiration Date\"],\"M1RnFv\":[\"Expired\"],\"i9qiyR\":[\"Expires in\"],\"GS+Mus\":[\"Export\"],\"G5DJkP\":[\"Export record\"],\"ep2rbf\":[\"Export records\"],\"iHK6np\":[\"Export run\"],\"vwtAUW\":[\"Export runs\"],\"q46CjD\":[\"Export to PDF\"],\"BuprEs\":[\"Export version\"],\"4FLUle\":[\"Export versions\"],\"DaGxE0\":[\"Export view\"],\"+FMXdE\":[\"Export workflow\"],\"XcAij/\":[\"Export workflows\"],\"eWCNmu\":[\"Export Workflows\"],\"uE7S0L\":[\"Extra Credits Used\"],\"bVax4d\":[\"Failed to remove  override\"],\"A/P7PX\":[\"Failed to remove override\"],\"Dzh4a2\":[\"Failed to update variable\"],\"jARNNi\":[\"Failed to validate API key. Please check your API key and try again.\"],\"ocUvR+\":[\"False\"],\"X9kySA\":[\"Favorites\"],\"YXjpZx\":[\"Feature Flag\"],\"kP/brT\":[\"Feature Flags & Impersonation\"],\"nrXDdR\":[\"Field name\"],\"fV7qkH\":[\"Field Name\"],\"zXgopL\":[\"Field type\"],\"vF68cg\":[\"Fields\"],\"3w/aqw\":[\"Fields Count\"],\"o7J4JM\":[\"Filter\"],\"cSev+j\":[\"Filters\"],\"JmZ/+d\":[\"Finish\"],\"SNdnlf\":[\"Finish flow with errors\"],\"USZ2N6\":[\"Finnish\"],\"ZyIk6Y\":[\"First 10 lines\"],\"I3hko2\":[\"First 2 lines\"],\"BDDkm3\":[\"First 5 lines\"],\"kODvZJ\":[\"First Name\"],\"7JBW66\":[\"Forbidden\"],\"glx6on\":[\"Forgot your password?\"],\"kI1qVD\":[\"Format\"],\"abDZwc\":[\"Format e.g. d-MMM-y (qqq'yy)\"],\"wjsFMQ\":[\"Free Credits Used\"],\"nLC6tu\":[\"French\"],\"aTieE0\":[\"from monthly to yearly\"],\"K04lE5\":[\"from yearly to monthly\"],\"scmRyR\":[\"Full access\"],\"xANKBj\":[\"Functions\"],\"Weq9zb\":[\"General\"],\"DDcvSo\":[\"German\"],\"NXEW3h\":[\"Get the most out of your workspace by inviting your team.\"],\"zSGbaR\":[\"Get your subscription\"],\"2GT3Hf\":[\"Global\"],\"mUbv8L\":[\"Go to Companies\"],\"dHvI9O\":[\"Go to Draft\"],\"BQE4ob\":[\"Go to Notes\"],\"buPcNi\":[\"Go to Opportunities\"],\"MrE/Qb\":[\"Go to People\"],\"iS69s6\":[\"Go to runs\"],\"mT57+Q\":[\"Go to Settings\"],\"BM24SF\":[\"Go to Tasks\"],\"A5WHZY\":[\"Go to workflows\"],\"6eo66Q\":[\"Grant access to your workspace so we can troubleshoot problems.\"],\"hWp1MY\":[\"Grant Twenty support temporary access to your workspace so we can troubleshoot problems or recover content on your behalf. You can revoke access at any time.\"],\"MM43i1\":[\"Granted for \",[\"grantedBy\"],\" \",[\"pluralizedGrantedObject\"]],\"WUgtid\":[\"Granted for this object\"],\"gBiL6J\":[\"GraphQL\"],\"/YalkJ\":[\"GraphQL API Playground\"],\"JXaffl\":[\"Greater than\"],\"FJWI9L\":[\"Greater than or equal\"],\"CZXzs4\":[\"Greek\"],\"L8fEEm\":[\"Group\"],\"ALoP4W\":[\"Group by\"],\"I1IOmb\":[\"Health Status\"],\"3oTCgM\":[\"Hebrew\"],\"D+zLDD\":[\"Hidden\"],\"em+S/U\":[\"Hidden fields\"],\"oK+1Wj\":[\"Hidden Fields\"],\"vLyv1R\":[\"Hide\"],\"OlbYor\":[\"Hide deleted\"],\"FHhlye\":[\"Hide deleted records\"],\"0dZtKR\":[\"Hide deleted runs\"],\"Jc6FrI\":[\"Hide deleted versions\"],\"aOZAIB\":[\"Hide deleted workflows\"],\"HS8BG/\":[\"Hide empty groups\"],\"wgmt7A\":[\"Hide hidden groups\"],\"B06Bgk\":[\"How you'll be identified on the app.\"],\"k7iMla\":[\"How your system is doing\"],\"DvpBQM\":[\"HTTP Request\"],\"0yRnXS\":[\"https://example.com/webhook\"],\"mkWad2\":[\"Hungarian\"],\"wwu18a\":[\"Icon\"],\"XTWO+W\":[\"Icon and Name\"],\"S0kLOH\":[\"ID\"],\"sJGljQ\":[\"Identifier\"],\"06cbfQ\":[\"Identity Provider\"],\"LPN8Ma\":[\"Identity Provider Metadata XML\"],\"ZfP/Ap\":[\"If this is unexpected, please verify your settings.\"],\"j843N3\":[\"If you’ve lost this key, you can regenerate it, but be aware that any script using this key will need to be updated. Please type\\\"\",[\"confirmationValue\"],\"\\\" to confirm.\"],\"3rtE4o\":[\"IMAP Connection Details\"],\"jXXECN\":[\"IMAP connection successfully created\"],\"Fz/wXI\":[\"IMAP connection successfully updated\"],\"TE8s2c\":[\"IMAP Port\"],\"/RAwfY\":[\"IMAP Server\"],\"+8jOVa\":[\"imap.example.com\"],\"tSVr6t\":[\"Impersonate\"],\"l3s5ri\":[\"Import\"],\"eECp4f\":[\"Import records\"],\"ZsIcZZ\":[\"Import workflows\"],\"RqSvT0\":[\"Importing\"],\"YgmSaz\":[\"Importing Data ...\"],\"NoNwIX\":[\"Inactive\"],\"pZ/USH\":[\"Indexes\"],\"JE2tjr\":[\"Input must be in camel case and cannot start with a number\"],\"/6i28D\":[\"Input settings\"],\"AwUsnG\":[\"Instances\"],\"g+29+d\":[\"Instructions for AI\"],\"nbfdhU\":[\"Integrations\"],\"GNRDhm\":[\"Invalid API key\"],\"NtFk/Z\":[\"Invalid auth provider\"],\"qcXnvu\":[\"Invalid custom domain. Custom domains have to be smaller than 256 characters in length, cannot be IP addresses, cannot contain spaces, cannot contain any special characters such as _~`!@#$%^*()=+{}[]|\\\\;:'\\\",<>/? and cannot begin or end with a '-' character.\"],\"P3qQyo\":[\"Invalid custom domain. Please include at least one subdomain (e.g., sub.example.com).\"],\"u3hwhx\":[\"Invalid domain. Domains have to be smaller than 256 characters in length, cannot be IP addresses, cannot contain spaces, cannot contain any special characters such as _~`!@#$%^*()=+{}[]|\\\\;:'\\\",<>/? and cannot begin or end with a '-' character.\"],\"B2Tpo0\":[\"Invalid email\"],\"/m52AE\":[\"Invalid email or domain\"],\"b2B7Ze\":[\"Invalid email verification link.\"],\"uzxr9u\":[\"Invalid File\"],\"QdoUFL\":[\"Invalid form values\"],\"K8XJhc\":[\"Invalid workspace\"],\"ZR1dJ4\":[\"Invitations\"],\"MFKlMB\":[\"Invite\"],\"0M8+El\":[\"Invite by email\"],\"PWIq/W\":[\"Invite by link\"],\"3athPG\":[\"Invite by Link\"],\"5IfmKA\":[\"Invite link sent to email addresses\"],\"x1m5RZ\":[\"Invite user\"],\"d+Y+rP\":[\"Invite your team\"],\"W153SA\":[\"Is\"],\"N73SBG\":[\"Is after\"],\"NgIlDJ\":[\"Is before\"],\"Hte7bc\":[\"Is empty\"],\"glSyvW\":[\"Is in future\"],\"F9Vw4E\":[\"Is in past\"],\"5pz6vU\":[\"Is not\"],\"/2on+O\":[\"is not a valid array\"],\"gNfGMx\":[\"is not a valid calling code\"],\"Ka30cp\":[\"is not a valid country code\"],\"LmaaLR\":[\"is not a valid date (format: '2021-12-01')\"],\"ntHJJY\":[\"is not a valid date time (format: '2021-12-01T00:00:00Z')\"],\"M9i8dv\":[\"is not a valid email\"],\"96g038\":[\"is not a valid JSON\"],\"l5P+Rf\":[\"is not a valid phone number\"],\"Ir+3AW\":[\"is not a valid URL\"],\"MgIflq\":[\"is not a valid UUID\"],\"ldI7NO\":[\"Is not empty\"],\"HQFVAU\":[\"Is not null\"],\"jPtV7x\":[\"Is relative\"],\"0TLhix\":[\"Is today\"],\"IhCN5p\":[\"Issuer URI\"],\"Lj7sBL\":[\"Italian\"],\"dFtidv\":[\"Japanese\"],\"wrLOeW\":[\"<EMAIL>\"],\"VIK/N0\":[\"JSON keys cannot contain spaces\"],\"OGXtL8\":[\"Kanban\"],\"h6S9Yz\":[\"Korean\"],\"zrpwCd\":[\"Lab\"],\"vXIe7J\":[\"Language\"],\"DU27lO\":[\"Last 1 hour\"],\"0LzSIl\":[\"Last 1 Hour (oldest → newest)\"],\"XUlP/Y\":[\"Last 12 hours\"],\"n7EOpf\":[\"Last 12 Hours (oldest → newest)\"],\"j5nqnO\":[\"Last 24 Hours (oldest → newest)\"],\"gdAJ4I\":[\"Last 4 hours\"],\"MjAVB0\":[\"Last 4 Hours (oldest → newest)\"],\"GQgBSj\":[\"Last 7 Days (oldest → newest)\"],\"UXBCwc\":[\"Last Name\"],\"s0kiVA\":[\"Last payment failed. Please contact your admin.\"],\"3u/3zO\":[\"Last payment failed. Please update your billing details.\"],\"wL3cK8\":[\"Latest\"],\"Kcjbmz\":[\"Latest date\"],\"zUzYWu\":[\"Latest version\"],\"7dWjtB\":[\"Latest version:\"],\"ZEP8tT\":[\"Launch\"],\"rdU729\":[\"Layout\"],\"haJ2Hb\":[\"Less than\"],\"8PPI2g\":[\"Less than or equal\"],\"1njn7W\":[\"Light\"],\"pQjjYo\":[\"Link copied to clipboard\"],\"DL2sg0\":[\"Listings\"],\"Nx262D\":[\"Loading csv ... \"],\"s1MstR\":[\"Loading metrics data...\"],\"Z3FXyt\":[\"Loading...\"],\"FgAxTj\":[\"Log out\"],\"nOhz3x\":[\"Logout\"],\"PTozs8\":[\"Look up users and manage their workspace feature flags or impersonate them.\"],\"GPSwzy\":[\"Look up users to impersonate them.\"],\"n0FHLv\":[\"Manage API keys and webhooks\"],\"nvgUPq\":[\"Manage billing information\"],\"U8dG4j\":[\"Manage favorite\"],\"T6YjCk\":[\"Manage Members\"],\"eGGH1l\":[\"Manage security policies\"],\"49MfD1\":[\"Manage support access settings\"],\"4cjU2u\":[\"Manage the members of your space here\"],\"N7fMy9\":[\"Manage the members of your workspace here\"],\"fEqDWx\":[\"Manage workflows\"],\"FyFNsd\":[\"Manage your internet accounts.\"],\"36kYu0\":[\"Manage your subscription\"],\"3Sdni6\":[\"Mark as done\"],\"7h8ch8\":[\"Match columns\"],\"gDLior\":[\"Match Columns\"],\"CK1KXz\":[\"Max\"],\"HFjNu6\":[\"Max import capacity: \",[\"formatSpreadsheetMaxRecordImportCapacity\"],\" records. Otherwise, consider splitting your file or using the API.\"],\"JGZq0R\":[\"Max import capacity: \",[\"SpreadsheetMaxRecordImportCapacity\"],\" records. Otherwise, consider splitting your file or using the API.\"],\"Sma10A\":[\"Member of\"],\"wlQNTg\":[\"Members\"],\"U15XwX\":[\"Message not found\"],\"elpx6r\":[\"Message Sync\"],\"Lt5rXo\":[\"Message Sync Status\"],\"6GBt0m\":[\"Metadata\"],\"dN5YOb\":[\"Metadata file generation failed\"],\"eTUF28\":[\"Min\"],\"8g3Cgz\":[\"Monitor the execution of your calendar events sync job\"],\"IL/nIb\":[\"Monitor the execution of your emails sync job\"],\"+8Nek/\":[\"Monthly\"],\"1cOZuL\":[\"Monthly Credits\"],\"2FYpfJ\":[\"More\"],\"3Siwmw\":[\"More options\"],\"iSLA/r\":[\"Move left\"],\"Ubl2by\":[\"Move right\"],\"asWVA5\":[\"must be a number\"],\"qJVW/e\":[\"must be an array of object with valid phone, calling code and country code (format: '[{\\\"number\\\":\\\"123456789\\\", \\\"callingCode\\\":\\\"+33\\\", \\\"countryCode\\\":\\\"FR\\\"}]')\"],\"hTW2Fw\":[\"must be an array of object with valid url and label (format: '[{\\\"url\\\":\\\"valid.url\\\", \\\"label\\\":\\\"label value\\\")}]'\"],\"5rovyq\":[\"must be an array of valid emails\"],\"amJfUq\":[\"must contain only numbers\"],\"6YtxFj\":[\"Name\"],\"XSwyCU\":[\"Name can not be empty\"],\"zaxmAs\":[\"Name in both singular (e.g., 'Invoice') and plural (e.g., 'Invoices') forms.\"],\"z+6jaZ\":[\"Name of your API key\"],\"J7w8lI\":[\"Name of your workspace\"],\"2T8KCk\":[\"Navigate to next record\"],\"UX6+vb\":[\"Navigate to next run\"],\"veSA19\":[\"Navigate to next version\"],\"ZTEho+\":[\"Navigate to next workflow\"],\"2tw9bo\":[\"Navigate to previous record\"],\"HddE65\":[\"Navigate to previous run\"],\"I+Pm5V\":[\"Navigate to previous version\"],\"QVUN3K\":[\"Navigate to previous workflow\"],\"isRobC\":[\"New\"],\"Gntx7w\":[\"New \",[\"capitalizedObjectNameSingular\"]],\"Kcr9Fr\":[\"New account\"],\"2qr/61\":[\"New Approved Access Domain\"],\"8YPqRx\":[\"New Field\"],\"GeB/fA\":[\"New IMAP Connection\"],\"o8MyXb\":[\"New key\"],\"j313SZ\":[\"New Key\"],\"hFxdey\":[\"New Object\"],\"7vhWI8\":[\"New Password\"],\"BcCzLv\":[\"New record\"],\"2lmOC5\":[\"New Role\"],\"OzWuT+\":[\"New SSO Configuration\"],\"C7WtCv\":[\"New SSO provider\"],\"U1DAok\":[\"New Webhook\"],\"AxNmtI\":[\"Next Step\"],\"QNbLNQ\":[\"No AI models available\"],\"OTe3RI\":[\"No change detected\"],\"YFLMyY\":[\"No config variables match your current filters. Try adjusting your filters or search criteria.\"],\"pwenQu\":[\"No connected account\"],\"4BSfjK\":[\"No country\"],\"7tCnBa\":[\"No Data Available for Remote Table\"],\"pxvJ9B\":[\"No data containing errors\"],\"dihZwh\":[\"No data found\"],\"xlUOps\":[\"No free workflow executions left. End trial period and activate your billing to continue.\"],\"pxYUeX\":[\"No free workflow executions left. Please contact your admin.\"],\"OcMef3\":[\"No latest version found\"],\"UwvrGq\":[\"No members\"],\"Y4qK8/\":[\"No members assigned\"],\"hfYSED\":[\"No members assigned to this role yet\"],\"/nLvVj\":[\"No members match your search\"],\"F9pWel\":[\"No members matching this search\"],\"NluSN3\":[\"No members matching your search\"],\"0NudpV\":[\"No metrics data available\"],\"iMCnTm\":[\"No more members to add\"],\"DL8pzn\":[\"No more members to assign\"],\"daCzI1\":[\"No option found\"],\"tTItk7\":[\"No output available\"],\"fE7upK\":[\"No overrides found\"],\"5pSj4j\":[\"No payment method found. Please update your billing details.\"],\"CfOZmU\":[\"No permissions found\"],\"EqGTpW\":[\"No records found\"],\"4bobEy\":[\"No Result\"],\"Ev2r9A\":[\"No results\"],\"MA3x23\":[\"No Results\"],\"qq788M\":[\"No role\"],\"9mcJ/7\":[\"No roles found\"],\"mmm8hk\":[\"No rows with errors\"],\"Hsl+kr\":[\"No Select field\"],\"A5XQ/A\":[\"No variables found\"],\"0uWxPM\":[\"No workflow runs yet\"],\"AQCvCC\":[\"No workflow versions yet\"],\"EdQY6l\":[\"None\"],\"1IipHp\":[\"Norwegian\"],\"v3W9iu\":[\"Not all columns matched\"],\"tqk+dw\":[\"Not available for default view\"],\"0qBE9S\":[\"Not available on Default View\"],\"4wUkDk\":[\"Not empty\"],\"pAtylB\":[\"Not Found\"],\"51G4/+\":[\"Not shared\"],\"uzFo/5\":[\"Not shared by \",[\"notSharedByFullName\"]],\"hZWthZ\":[\"Not synced\"],\"1DBGsz\":[\"Notes\"],\"HptUxX\":[\"Number\"],\"0fRFSb\":[\"Number of decimals\"],\"qg5nhQ\":[\"Number type\"],\"W0i24j\":[\"Object\"],\"Zrauom\":[\"Object destination\"],\"YJgmMZ\":[\"Object-Level\"],\"ZXhr6E\":[\"Object-Level Permissions\"],\"B3toQF\":[\"Objects\"],\"KNz3EF\":[\"Off the beaten path\"],\"zii2Qj\":[\"Only date & participants will be shared with your team.\"],\"50ETCF\":[\"Only the timestamp & participants will be shared with your team.\"],\"69b7aE\":[\"Open command menu\"],\"pZZH4Q\":[\"Open Gmail\"],\"C39K59\":[\"Open in\"],\"xGd6Ih\":[\"Open Outlook\"],\"avIMon\":[\"Open the ⌘K to trigger this workflow\"],\"OV5wZZ\":[\"Opened\"],\"4MyDFl\":[\"Opportunities\"],\"hY8F2i\":[\"Optional secret used to compute the HMAC signature for webhook payloads\"],\"qNELak\":[\"Optional: Define a secret string that we will include in every webhook. Use this to authenticate and verify the webhook upon receipt.\"],\"0zpgxV\":[\"Options\"],\"BzEFor\":[\"or\"],\"ucgZ0o\":[\"Organization\"],\"/IX/7x\":[\"Other\"],\"3V8SRM\":[\"Other Environment Variables\"],\"P7KMWx\":[\"Other Variables\"],\"+vDFPm\":[\"Other workspaces\"],\"p5ufK6\":[\"Our team can help you set up your workspace to match your specific needs and workflows.\"],\"xvfn7l\":[\"Output Field \",[\"fieldNumber\"]],\"bv8ZsZ\":[\"Overridden on \",[\"isOverriddenBy\"],\" \",[\"pluralizedObject\"]],\"boJlGf\":[\"Page Not Found\"],\"8ZsakT\":[\"Password\"],\"BxQ79w\":[\"Password has been updated\"],\"mi6Rel\":[\"Password reset link has been sent to the email\"],\"1wdjme\":[\"People\"],\"PxBA+g\":[\"People I’ve sent emails to and received emails from.\"],\"U/UvMm\":[\"People I’ve sent emails to.\"],\"SrVzRe\":[\"Percent\"],\"yIK1GU\":[\"Percent empty\"],\"PWLd4c\":[\"Percent not empty\"],\"/roQKz\":[\"Percentage\"],\"Bv3y5w\":[\"Permanently destroy record\"],\"xjWlSJ\":[\"Permanently destroy records\"],\"0bQ5ba\":[\"Permanently destroy workflow\"],\"uKWXhB\":[\"Permanently destroy workflows\"],\"UjCOlM\":[\"Permission overrides\"],\"9cDpsw\":[\"Permissions\"],\"KiuPPj\":[\"Permissions · \",[\"objectLabelSingular\"]],\"qlqT9z\":[\"Phone number copied to clipboard\"],\"FdlpPK\":[\"Pick a \",[\"nameSingular\"],\" record\"],\"N0+GsR\":[\"Picture\"],\"GdgCoi\":[\"Plan\"],\"0LrFTO\":[\"Playground\"],\"90/XJz\":[\"Please check your email for a verification link.\"],\"jEw0Mr\":[\"Please enter a valid URL\"],\"CjNkdJ\":[\"Please refresh the page.\"],\"X5x85V\":[\"Please search for a user first\"],\"6nsIo3\":[\"Please type \\\"\",[\"confirmationValue\"],\"\\\" to confirm you want to delete this API Key. Be aware that any script using this key will stop working.\"],\"GbtYRD\":[\"Please type \\\"yes\\\" to confirm you want to delete this webhook.\"],\"mFZTXr\":[\"Please type \",[\"confirmationText\"],\" to confirm you want to delete this webhook.\"],\"aRWD63\":[\"Please use different names for your source and destination fields\"],\"BPig2P\":[\"Plural\"],\"trnWaw\":[\"Polish\"],\"MOERNx\":[\"Portuguese\"],\"0nsqwk\":[\"Portuguese — Brazil\"],\"xtXHeo\":[\"Portuguese — Portugal\"],\"R7+D0/\":[\"Portuguese (Brazil)\"],\"512Uma\":[\"Portuguese (Portugal)\"],\"/IZFIg\":[\"Prefilling your workspace data\"],\"/v6Rp9\":[\"Prefilling your workspace data...\"],\"rdUucN\":[\"Preview\"],\"LcET2C\":[\"Privacy Policy\"],\"3fPjUY\":[\"Pro\"],\"k1ifdL\":[\"Processing...\"],\"vERlcd\":[\"Profile\"],\"GVxbU6\":[\"Provide your OIDC provider details\"],\"YJgRqq\":[\"Pseudo-English\"],\"/IUt/5\":[\"Queue information is not available because the worker is down\"],\"e9OqcR\":[\"Queue performance\"],\"ibPuCP\":[\"Read documentation\"],\"v3xM25\":[\"Receive an email containing password update link\"],\"ZVr1+K\":[\"Recent Events (oldest → newest)\"],\"gcoiFh\":[\"Reconnect\"],\"2CUci6\":[\"Record creation stopped. \",[\"formattedCreatedRecordsCount\"],\" records created.\"],\"mj1fkT\":[\"Record image\"],\"K6/7kH\":[\"Record label\"],\"mAHjRd\":[\"Record Page\"],\"dSCufP\":[\"Record Selection\"],\"LfH+Ea\":[\"Redirect Url copied to clipboard\"],\"RZjynQ\":[\"Redirection URI\"],\"vpZcGd\":[\"Regenerate an API key\"],\"Mwqo5m\":[\"Regenerate key\"],\"D+Mv78\":[\"Regenerate Key\"],\"UiAJoB\":[\"Relation type\"],\"HR+PwH\":[\"Relative\"],\"5icoS1\":[\"Releases\"],\"HpK/8d\":[\"Reload\"],\"t/YqKh\":[\"Remove\"],\"ken+P9\":[\"Remove \",[\"workspaceMemberName\"],\"?\"],\"1O32oy\":[\"Remove account\"],\"Q2u5E9\":[\"Remove as default\"],\"T/pF0Z\":[\"Remove from favorites\"],\"00Lxnh\":[\"Remove option\"],\"Ee8JBW\":[\"Reorder field\"],\"G42SNI\":[\"Resend email\"],\"OfhWJH\":[\"Reset\"],\"KbS2K9\":[\"Reset Password\"],\"1IWc1n\":[\"Reset to\"],\"Tj36Dr\":[\"Reset to Default\"],\"6rzsES\":[\"Reset variable\"],\"WHiaOl\":[\"REST\"],\"6z9W13\":[\"Restart\"],\"82zWno\":[\"Restart Import\"],\"yKu/3Y\":[\"Restore\"],\"y4Ib1n\":[\"Restore record\"],\"Vw369F\":[\"Restore records\"],\"kx0s+n\":[\"Results\"],\"BohSvm\":[\"Review your import\"],\"Wz7mbi\":[\"Revoked for \",[\"revokedBy\"],\" \",[\"pluralizedRevokedObject\"]],\"GG37FL\":[\"Revoked for this object\"],\"4+uhWL\":[\"Revoked on \",[\"revokedBy\"],\" \",[\"pluralizedObject\"]],\"iSfzXo\":[\"Role name\"],\"gF4nBH\":[\"Role name cannot be empty\"],\"5dJK4M\":[\"Roles\"],\"uJc01W\":[\"Romanian\"],\"UX0N2y\":[\"Run a workflow and return here to view its executions\"],\"nji0/X\":[\"Russian\"],\"tfDRzk\":[\"Save\"],\"apLRCm\":[\"Save as new view\"],\"QJ8HBJ\":[\"Schema\"],\"A1taO8\":[\"Search\"],\"8NBMeZ\":[\"Search '\",[\"commandMenuSearch\"],\"' with...\"],\"l1/uy2\":[\"Search a field...\"],\"t3n1Qy\":[\"Search a member\"],\"xdl79x\":[\"Search a team member...\"],\"lnDfeK\":[\"Search a type\"],\"x55IVv\":[\"Search an assigned team member...\"],\"k7kp5/\":[\"Search an index...\"],\"pFe0YS\":[\"Search an object\"],\"3UPqHL\":[\"Search config variables\"],\"WLfEv1\":[\"Search country\"],\"sSARNY\":[\"Search currency\"],\"7taA9j\":[\"Search fields\"],\"ofuw3g\":[\"Search for an object...\"],\"lnwW17\":[\"Search icon\"],\"IMeaSJ\":[\"Search records\"],\"8sgZS9\":[\"seat / month\"],\"aQnWwf\":[\"seat / month - billed yearly\"],\"grt0Pu\":[\"Seats\"],\"8VEDbV\":[\"Secret\"],\"e1v+J3\":[\"Secret (optional)\"],\"a3LDKx\":[\"Security\"],\"i0zibG\":[\"see\"],\"rJnbMG\":[\"See \",[\"objectLabel\"]],\"QREcJS\":[\"See active version\"],\"BxIUpp\":[\"See deleted records\"],\"Z+SGGW\":[\"See deleted runs\"],\"91fiCe\":[\"See deleted versions\"],\"5jZIe5\":[\"See deleted workflows\"],\"FvBwjM\":[\"See Records on \",[\"objectLabel\"]],\"irbH/8\":[\"See Records on All Objects\"],\"OpPn5Z\":[\"See runs\"],\"gGEfj/\":[\"See version\"],\"EtyY4+\":[\"See versions\"],\"lYhPN0\":[\"See versions history\"],\"qEKLJV\":[\"See workflow\"],\"XThiR2\":[\"See workflows\"],\"5S/Gqz\":[\"Select a record then open the ⌘K to trigger this workflow\"],\"WZvt/6\":[\"Select Action\"],\"hVPa4O\":[\"Select an option\"],\"21YIUv\":[\"Select column...\"],\"J6WI1B\":[\"Select file\"],\"llc/hA\":[\"Select Group\"],\"ljSvAs\":[\"Select header row\"],\"UKCiEd\":[\"Select Source\"],\"mWHn81\":[\"Select the correct field for each column you'd like to import.\"],\"pofGCP\":[\"Select the default value for this boolean field\"],\"xraglu\":[\"Select the events you wish to send to this endpoint\"],\"+xn1pe\":[\"Select the sheet to use\"],\"AXTJAW\":[\"Select your preferred language\"],\"mjK8F3\":[\"Send an invite email to your team\"],\"IoAuJG\":[\"Sending...\"],\"h69WC6\":[\"Sent\"],\"hdVMzO\":[\"Sent and Received\"],\"6oxz/y\":[\"Serbian (Cyrillic)\"],\"WKimFU\":[\"Server Admin\"],\"yy5k7a\":[\"Server Admin Panel\"],\"LUc0oL\":[\"Service Provider Details\"],\"YZwx1e\":[\"Set a default role for this workspace\"],\"M97je1\":[\"Set additional object-level permissions\"],\"PPcets\":[\"Set as default\"],\"V7fgiB\":[\"Set email visibility, manage your blocklist and more.\"],\"6KA8xy\":[\"Set global workspace preferences\"],\"qNbuWB\":[\"Set the name of your custom domain and configure your DNS records.\"],\"cx14rp\":[\"Set the name of your domain\"],\"tn41zE\":[\"Set the name of your subdomain\"],\"oG3fDz\":[\"Setting up your database\"],\"dQsqpC\":[\"Setting up your database...\"],\"Tz0i8g\":[\"Settings\"],\"uXW4pg\":[\"Settings All Access\"],\"p8fNBm\":[\"Settings permissions\"],\"Vy9kmk\":[\"Share this link to invite users to join your workspace\"],\"RRXpo1\":[\"Short\"],\"gWk8gY\":[\"Should changing a field's label also change the API name?\"],\"WFtdWr\":[\"Should changing an object's label also change the API?\"],\"1OVrop\":[\"Show hidden groups\"],\"qi+g0a\":[\"Show only rows with errors\"],\"MHlGJL\":[\"Show unmatched columns\"],\"uVg8dY\":[\"Side Panel\"],\"5lWFkC\":[\"Sign in\"],\"n1ekoW\":[\"Sign In\"],\"e+RpCP\":[\"Sign up\"],\"mErq7F\":[\"Sign Up\"],\"5v3IHX\":[\"Single sign-on (SSO)\"],\"maCaRp\":[\"Singular\"],\"djfBXF\":[\"Singular and plural labels must be different\"],\"zvwLTy\":[\"Singular and plural names must be different\"],\"6Uau97\":[\"Skip\"],\"f6Hub0\":[\"Sort\"],\"wdxz7K\":[\"Source\"],\"65A04M\":[\"Spanish\"],\"vnS6Rf\":[\"SSO\"],\"vlvAkg\":[\"SSO (SAML / OIDC)\"],\"ErU1td\":[\"Stages\"],\"TJBHlP\":[\"Standard\"],\"uAQUqI\":[\"Status\"],\"qn1Xxf\":[\"Stored in database\"],\"ku9TbG\":[\"Subdomain\"],\"omhc+7\":[\"Subdomain already taken\"],\"OlC/tU\":[\"Subdomain can not be longer than 30 characters\"],\"ZETwlU\":[\"Subdomain can not be shorter than 3 characters\"],\"DTG2nE\":[\"Subdomain updated\"],\"97eDN2\":[\"Subject and metadata\"],\"LYbP/A\":[\"Subject and metadata will be shared with your team.\"],\"DvSvG9\":[\"Subject not shared\"],\"J9ylmk\":[\"Subject, body and attachments will be shared with your team.\"],\"hQRttt\":[\"Submit\"],\"EDl9kS\":[\"Subscribe\"],\"WVzGc2\":[\"Subscription\"],\"uvY7Wk\":[\"Subscription activated.\"],\"B5jRKH\":[\"Subscription has been switched \",[\"to\"]],\"KNMVB5\":[\"Subscription has been switched to Organization Plan.\"],\"TXH0ad\":[\"Subscription has been switched to yearly.\"],\"A5YO8f\":[\"Subscription has been switched to Yearly.\"],\"nyQWMb\":[\"Suggested\"],\"AxQiPW\":[\"Sum\"],\"XYLcNv\":[\"Support\"],\"UaISq3\":[\"Swedish\"],\"9yk9d1\":[\"Switch \",[\"from\"]],\"qi74XZ\":[\"Switch \",[\"to\"]],\"L6Fg36\":[\"Switch billing \",[\"to\"]],\"o04bjR\":[\"Switch billing to yearly\"],\"/IYUDq\":[\"Switch from monthly to yearly\"],\"E4rPXj\":[\"Switch Plan\"],\"lz+f/E\":[\"Switch to \",[\"alternatePlanName\"]],\"GCMizN\":[\"Switch to Organization\"],\"1EwZPZ\":[\"Switch to yearly\"],\"eCX1DT\":[\"Switch to Yearly\"],\"5TRY4+\":[\"Sync failed\"],\"N2FcBE\":[\"Synced\"],\"AtzMpB\":[\"Synchronize Field Label and API Name\"],\"WZ6bN9\":[\"Synchronize Objects Labels and API Names\"],\"D4SseJ\":[\"System settings\"],\"E3AMmw\":[\"System settings - \",[\"systemDateFormatLabel\"]],\"0ZgB1e\":[\"System Settings - \",[\"systemTimeFormatLabel\"]],\"4hJhzz\":[\"Table\"],\"GtycJ/\":[\"Tasks\"],\"xowcRf\":[\"Terms of Service\"],\"NnH3pK\":[\"Test\"],\"bU9B27\":[\"Test Workflow\"],\"xeiujy\":[\"Text\"],\"lyaiTc\":[\"Text copied to clipboard\"],\"1xQkU9\":[\"The default country code for new phone numbers.\"],\"PmXLtL\":[\"The default country for new addresses\"],\"2OUtmv\":[\"The description of this field\"],\"VGZYbZ\":[\"The email associated to your account\"],\"diO3fm\":[\"The icon your workflow trigger will display in the command menu\"],\"h8mvCd\":[\"The name and icon of this field\"],\"e/dfFe\":[\"The name of your connection\"],\"8EkdZh\":[\"The name of your Domain\"],\"+C8Rdp\":[\"The name of your organization\"],\"L97sPr\":[\"The page you're seeking is either gone or never was. Let's get you back on track\"],\"uWikAA\":[\"The values of this field\"],\"+69KDk\":[\"The values of this field must be unique\"],\"MHLapp\":[\"The whole event details will be shared with your team.\"],\"MssRHl\":[\"The workspace needs at least one Admin\"],\"FEr96N\":[\"Theme\"],\"6tNbxl\":[\"Theme \"],\"/cSDOy\":[\"There are required columns that are not matched or ignored. Do you want to continue?\"],\"luL9RX\":[\"There are still some rows that contain errors. Rows with errors will be ignored when submitting.\"],\"ynfkhb\":[\"There was an error while updating password.\"],\"PmtLRf\":[\"There was an issue\"],\"DHjmMm\":[\"These are only the server values. Ensure your worker environment has the same variables and values, this is required for asynchronous tasks like email sync.\"],\"Kb1oAw\":[\"This \",[\"columnName\"],\" value already exists in your import data\"],\"hqCwGc\":[\"This action cannot be undone. This will permanently delete this user and remove them from all their assignments.\"],\"gWGuHC\":[\"This action cannot be undone. This will permanently delete your entire workspace. <0/> Please type in your email to confirm.\"],\"SLIRqz\":[\"This database value overrides environment settings. \"],\"yHIStW\":[\"This member will be unassigned from this role.\"],\"vbLBMd\":[\"This role can \",[\"humanReadableAction\"],\" all records\"],\"/tr8Uy\":[\"This Role has the following permissions.\"],\"6j5nJX\":[\"This Role is assigned to these workspace member.\"],\"xPfDRx\":[\"This role is assigned to these workspace members.\"],\"C/wr0z\":[\"This setting can only be configured through environment variables.\"],\"iAubP2\":[\"This should never happen\"],\"MMGDfe\":[\"This value will be saved to the database.\"],\"yByRxz\":[\"This week\"],\"4YifBe\":[\"This will revert the database value to environment/default value. The database override will be removed and the system will use the environment settings.\"],\"n9nSNJ\":[\"Time format\"],\"Mz2JN2\":[\"Time zone\"],\"/VwdtK\":[\"Timestamp and participants will be shared with your team.\"],\"aqMzDX\":[\"to monthly\"],\"WXXiXO\":[\"to yearly\"],\"ecUA8p\":[\"Today\"],\"XArpJK\":[\"Toggle all object permissions\"],\"/kWolQ\":[\"Toggle all settings permissions\"],\"BRMXj0\":[\"Tomorrow\"],\"xdA/+p\":[\"Tools\"],\"vb0Q0/\":[\"Total Users\"],\"dshGKq\":[\"Track your monthly workflow credit consumption.\"],\"wN0jHF\":[\"Trial expired. Please contact your admin\"],\"y985qL\":[\"Trial expired. Please update your billing details.\"],\"PiUt3N\":[\"Trigger Type\"],\"IQ5pAL\":[\"Trigger type should be Manual - when no record(s) are selected\"],\"c+xCSz\":[\"True\"],\"haaL9N\":[\"Try our REST or GraphQL API playgrounds.\"],\"LQdi+H\":[\"Try with another email\"],\"Kz91g/\":[\"Turkish\"],\"+zy2Nq\":[\"Type\"],\"U83IeL\":[\"Type anything\"],\"V9+2pH\":[\"Ukrainian\"],\"wSXm5S\":[\"Unique\"],\"Ef7StM\":[\"Unknown\"],\"29VNqC\":[\"Unknown error\"],\"GQCXQS\":[\"Unlimited contacts\"],\"xzOKup\":[\"Untitled role\"],\"KmUc2w\":[\"Untitled Role\"],\"Y8zko3\":[\"update\"],\"EkH9pt\":[\"Update\"],\"Vmsh9w\":[\"Update your IMAP email account configuration\"],\"+b7T3G\":[\"Updated\"],\"ONWvwQ\":[\"Upload\"],\"7OP1Xi\":[\"Upload .xlsx, .xls or .csv file\"],\"2IXDgU\":[\"Upload file\"],\"IQ3gAw\":[\"Upload File\"],\"akDOEO\":[\"Upload the XML file with your connection infos\"],\"IagCbF\":[\"URL\"],\"6dMpmz\":[\"Use as draft\"],\"oTTQsc\":[\"Use letter, number and dash only. Start and finish with a letter or a number\"],\"c6uZUV\":[\"Use our API or add your first \",[\"objectLabel\"],\" manually\"],\"7PzzBU\":[\"User\"],\"YFciqL\":[\"User Email\"],\"GjhOGB\":[\"User ID\"],\"RNv3YS\":[\"User Impersonation\"],\"tNT8wT\":[\"User Info\"],\"IjyOjp\":[\"User is not logged in\"],\"5ZYU8G\":[\"User Name\"],\"Sxm8rQ\":[\"Users\"],\"vUr1/5\":[\"Using default application value. Configure via environment variables.\"],\"8Drj7i\":[\"Using default value. Set a custom value to override.\"],\"MGeZsN\":[\"Validate data\"],\"Clr4qp\":[\"Validate Data\"],\"wMHvYH\":[\"Value\"],\"UZHvVi\":[\"Value is set in the server environment, it may be a different value on the worker.\"],\"fXVIZq\":[\"Values\"],\"rawKOG\":[\"Variable deleted successfully.\"],\"dY/1ir\":[\"Variable not found\"],\"N/89ho\":[\"Variable updated successfully\"],\"2ushgC\":[\"Variable updated successfully.\"],\"IHIWR4\":[\"Version of the application\"],\"fROFIL\":[\"Vietnamese\"],\"jpctdh\":[\"View\"],\"KANz0G\":[\"View billing details\"],\"igR+P/\":[\"View execution details\"],\"qZmd6a\":[\"View settings\"],\"bJAIqT\":[\"View type\"],\"2q/Q7x\":[\"Visibility\"],\"oh8+os\":[\"Visible\"],\"zYTdqe\":[\"Visible fields\"],\"JiDDG4\":[\"Visible groups\"],\"6n7jtr\":[\"Visualize\"],\"zFSQY3\":[\"was created by\"],\"uHpu1c\":[\"was deleted by\"],\"S0enzB\":[\"was restored by\"],\"6eMAkI\":[\"We encountered an issue verifying\"],\"id6ein\":[\"We support your square PNGs, JPEGs and GIFs under 10MB\"],\"ZS7vYp\":[\"We will send POST requests to this endpoint for every new event\"],\"PI4LiB\":[\"We will send your a link to verify domain ownership\"],\"TRDppN\":[\"Webhook\"],\"Tvdycs\":[\"Webhook \",[\"targetUrl\"],\" created successfully\"],\"otr01y\":[\"Webhook \",[\"targetUrl\"],\" updated successfully\"],\"2X4ecw\":[\"Webhook deleted successfully\"],\"8bfORM\":[\"Webhook ID is required for deletion\"],\"+Fb+7w\":[\"Webhook ID is required for updates\"],\"v1kQyJ\":[\"Webhooks\"],\"Jt1WNL\":[\"Welcome to\"],\"ke0EDP\":[\"Welcome to \",[\"workspaceName\"]],\"C51ilI\":[\"When the API key will expire.\"],\"leUubq\":[\"When the key will be disabled\"],\"RfrIUU\":[\"Will be saved as:\"],\"e9sagb\":[\"Will return one \",[\"objectType\"],\" to the next step of this workflow\"],\"wvyffT\":[\"Workflow cannot be tested\"],\"GpJjC8\":[\"Workflow is running...\"],\"o0xBLi\":[\"Workflow run failed\"],\"woYYQq\":[\"Workflows\"],\"pmUArF\":[\"Workspace\"],\"VicISP\":[\"Workspace Deletion\"],\"J22jAC\":[\"Workspace Info\"],\"Y0Fj4S\":[\"Workspace logo\"],\"CozWO1\":[\"Workspace name\"],\"6X+cfX\":[\"Workspace Name\"],\"pmt7u4\":[\"Workspaces\"],\"5iSD9O\":[\"Wrap on record pages\"],\"Q9pNST\":[\"Write a description\"],\"L80fMJ\":[\"Write a secret\"],\"zkWmBh\":[\"Yearly\"],\"3d1wCB\":[\"yes\"],\"y/0uwd\":[\"Yesterday\"],\"lWUeEt\":[\"You already belong to the workspace \",[\"workspaceDisplayName\"]],\"Q1CQiy\":[\"You already belong to this workspace\"],\"qsAug0\":[\"You are already importing this column.\"],\"MFDARJ\":[\"You are not allowed to create records for this object\"],\"5eVYbs\":[\"You are not allowed to create records in this object\"],\"TBApzn\":[\"You do not have access to impersonate users.\"],\"cdk3eC\":[\"You haven't configured any model provider. Please set up an API Key in your instance's admin panel or as an environment variable.\"],\"zEM7Ne\":[\"You will be charged $\",[\"enterprisePrice\"],\" per user per month billed annually.\"],\"HM4D8s\":[\"You will be charged $\",[\"enterprisePrice\"],\" per user per month.\"],\"o4xIH4\":[\"You will be charged $\",[\"yearlyPrice\"],\" per user per month billed annually. A prorata with your current subscription will be applied.\"],\"zSkMV0\":[\"You will be charged immediately for the full year.\"],\"C2d+Xk\":[\"You will lose all your mappings.\"],\"Cl+hUj\":[\"You're about to change your workspace subdomain. This action will log out all users.\"],\"XVnj6K\":[\"Your credit balance will be used to pay the monthly bills.\"],\"+5YqGH\":[\"Your email subjects and meeting titles will be shared with your team.\"],\"la0RPA\":[\"Your emails and events content will be shared with your team.\"],\"9ivpwk\":[\"Your name as it will be displayed\"],\"3RASGN\":[\"Your name as it will be displayed on the app\"],\"YQK8fJ\":[\"Your Workspace\"],\"oC6WBs\":[\"Your workspace doesn't have an active subscription.\"],\"QBTEtW\":[\"Your workspace doesn't have an active subscription. Please contact your admin.\"],\"RhNbPE\":[\"Your workspace will be disabled\"]}")as Messages;