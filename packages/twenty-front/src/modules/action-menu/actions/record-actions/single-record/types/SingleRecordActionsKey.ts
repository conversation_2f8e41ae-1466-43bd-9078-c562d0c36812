export enum SingleRecordActionKeys {
  DELETE = 'delete-single-record',
  DESTROY = 'destroy-single-record',
  ADD_TO_FAVORITES = 'add-to-favorites-single-record',
  REMOVE_FROM_FAVORITES = 'remove-from-favorites-single-record',
  NAVIGATE_TO_NEXT_RECORD = 'navigate-to-next-record-single-record',
  NAVIGATE_TO_PREVIOUS_RECORD = 'navigate-to-previous-record-single-record',
  EXPORT_NOTE_TO_PDF = 'export-note-to-pdf-single-record',
  EXPORT_FROM_RECORD_INDEX = 'export-from-record-index-single-record',
  EXPORT_FROM_RECORD_SHOW = 'export-from-record-show-single-record',
  RESTORE = 'restore-single-record',
}
