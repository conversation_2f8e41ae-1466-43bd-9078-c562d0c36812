type ProductDataSeed = {
  id: string;
  name: string;
  sku: string;
  description: string;
  price: number;
  stockQuantity: number;
  category: string;
  isActive: boolean;
};

export const PRODUCT_DATA_SEED_COLUMNS: (keyof ProductDataSeed)[] = [
  'id',
  'name',
  'sku',
  'description',
  'price',
  'stockQuantity',
  'category',
  'isActive',
];

export const PRODUCT_DATA_SEED_IDS = {
  ID_1: '20202020-1111-4dd5-ac61-3315bc559e1a',
  ID_2: '20202020-2222-4dd5-ac61-3315bc559e2b',
  ID_3: '20202020-3333-4dd5-ac61-3315bc559e3c',
  ID_4: '20202020-4444-4dd5-ac61-3315bc559e4d',
  ID_5: '20202020-5555-4dd5-ac61-3315bc559e5e',
};

export const PRODUCT_DATA_SEEDS: ProductDataSeed[] = [
  {
    id: PRODUCT_DATA_SEED_IDS.ID_1,
    name: 'MacBook Pro 16"',
    sku: 'MBP-16-001',
    description: 'High-performance laptop with M3 Pro chip, perfect for professional work and creative tasks.',
    price: 2499.99,
    stockQuantity: 25,
    category: 'Electronics',
    isActive: true,
  },
  {
    id: PRODUCT_DATA_SEED_IDS.ID_2,
    name: 'iPhone 15 Pro',
    sku: 'IPH-15P-001',
    description: 'Latest iPhone with titanium design, advanced camera system, and A17 Pro chip.',
    price: 999.99,
    stockQuantity: 50,
    category: 'Electronics',
    isActive: true,
  },
  {
    id: PRODUCT_DATA_SEED_IDS.ID_3,
    name: 'AirPods Pro (3rd Gen)',
    sku: 'APP-3G-001',
    description: 'Premium wireless earbuds with active noise cancellation and spatial audio.',
    price: 249.99,
    stockQuantity: 100,
    category: 'Audio',
    isActive: true,
  },
  {
    id: PRODUCT_DATA_SEED_IDS.ID_4,
    name: 'Magic Mouse',
    sku: 'MM-001',
    description: 'Wireless mouse with multi-touch surface and rechargeable battery.',
    price: 79.99,
    stockQuantity: 75,
    category: 'Accessories',
    isActive: true,
  },
  {
    id: PRODUCT_DATA_SEED_IDS.ID_5,
    name: 'iPad Air (Discontinued)',
    sku: 'IPA-OLD-001',
    description: 'Previous generation iPad Air - no longer in production.',
    price: 599.99,
    stockQuantity: 0,
    category: 'Electronics',
    isActive: false,
  },
];
