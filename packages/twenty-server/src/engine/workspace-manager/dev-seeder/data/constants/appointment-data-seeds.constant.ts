type AppointmentDataSeed = {
  id: string;
  description: string;
  startTime: string;
  endTime: string;
  duration: number;
  location: string;
  appointmentType: string;
  mode: string;
  status: string;
  checkinTime: string | null;
};

export const APPOINTMENT_DATA_SEED_COLUMNS: (keyof AppointmentDataSeed)[] = [
  'id',
  'description',
  'startTime',
  'endTime',
  'duration',
  'location',
  'appointmentType',
  'mode',
  'status',
  'checkinTime',
];

export const APPOINTMENT_DATA_SEED_IDS = {
  ID_1: '20202020-0001-0000-0000-000000000001',
  ID_2: '20202020-0002-0000-0000-000000000002',
  ID_3: '20202020-0003-0000-0000-000000000003',
  ID_4: '20202020-0004-0000-0000-000000000004',
  ID_5: '20202020-0005-0000-0000-000000000005',
  ID_6: '20202020-0006-0000-0000-000000000006',
};

export const APPOINTMENT_DATA_SEEDS: AppointmentDataSeed[] = [
  {
    id: APPOINTMENT_DATA_SEED_IDS.ID_1,
    description: 'Comprehensive health examination including blood tests and physical assessment',
    startTime: '2024-07-10T09:00:00.000Z',
    endTime: '2024-07-10T10:30:00.000Z',
    duration: 90,
    location: 'Medical Center, Room 205',
    appointmentType: 'Medical Consultation',
    mode: 'In-person',
    status: 'Scheduled',
    checkinTime: null,
  },
  {
    id: APPOINTMENT_DATA_SEED_IDS.ID_2,
    description: 'Initial meeting to discuss project requirements and timeline',
    startTime: '2024-07-08T14:00:00.000Z',
    endTime: '2024-07-08T15:00:00.000Z',
    duration: 60,
    location: 'Conference Room A',
    appointmentType: 'Business Meeting',
    mode: 'In-person',
    status: 'Completed',
    checkinTime: '2024-07-08T13:55:00.000Z',
  },
  {
    id: APPOINTMENT_DATA_SEED_IDS.ID_3,
    description: 'Discussion about contract terms and legal implications',
    startTime: '2024-07-12T11:00:00.000Z',
    endTime: '2024-07-12T12:00:00.000Z',
    duration: 60,
    location: 'Law Office, 15th Floor',
    appointmentType: 'Legal Consultation',
    mode: 'In-person',
    status: 'Scheduled',
    checkinTime: null,
  },
  {
    id: APPOINTMENT_DATA_SEED_IDS.ID_4,
    description: 'Daily team synchronization and progress updates',
    startTime: '2024-07-09T10:00:00.000Z',
    endTime: '2024-07-09T10:30:00.000Z',
    duration: 30,
    location: 'Zoom Meeting Room',
    appointmentType: 'Team Meeting',
    mode: 'Online',
    status: 'Completed',
    checkinTime: '2024-07-09T09:58:00.000Z',
  },
  {
    id: APPOINTMENT_DATA_SEED_IDS.ID_5,
    description: 'Routine dental cleaning and oral health checkup',
    startTime: '2024-07-15T16:00:00.000Z',
    endTime: '2024-07-15T17:00:00.000Z',
    duration: 60,
    location: 'Dental Clinic, Chair 3',
    appointmentType: 'Medical Consultation',
    mode: 'In-person',
    status: 'Scheduled',
    checkinTime: null,
  },
  {
    id: APPOINTMENT_DATA_SEED_IDS.ID_6,
    description: 'Presenting quarterly results and future strategy to key client',
    startTime: '2024-07-11T15:30:00.000Z',
    endTime: '2024-07-11T16:30:00.000Z',
    duration: 60,
    location: 'Client Office, Boardroom',
    appointmentType: 'Business Meeting',
    mode: 'In-person',
    status: 'Cancelled',
    checkinTime: null,
  },
];
