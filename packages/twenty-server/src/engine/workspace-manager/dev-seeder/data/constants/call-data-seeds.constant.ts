type CallDataSeed = {
  id: string;
  callerName: string;
  phoneNumber: string;
  callType: string;
  callStatus: string;
  callStartTime: string;
  callEndTime: string | null;
  duration: number;
  callPurpose: string;
  notes: string;
  followUpRequired: string;
};

export const CALL_DATA_SEED_COLUMNS: (keyof CallDataSeed)[] = [
  'id',
  'callerName',
  'phoneNumber',
  'callType',
  'callStatus',
  'callStartTime',
  'callEndTime',
  'duration',
  'callPurpose',
  'notes',
  'followUpRequired',
];

export const CALL_DATA_SEED_IDS = {
  ID_1: '40404040-0001-0000-0000-000000000001',
  ID_2: '40404040-0002-0000-0000-000000000002',
  ID_3: '40404040-0003-0000-0000-000000000003',
  ID_4: '40404040-0004-0000-0000-000000000004',
  ID_5: '40404040-0005-0000-0000-000000000005',
  ID_6: '40404040-0006-0000-0000-000000000006',
};

export const CALL_DATA_SEEDS: CallDataSeed[] = [
  {
    id: CALL_DATA_SEED_IDS.ID_1,
    callerName: 'Robert Johnson',
    phoneNumber: '******-0123',
    callType: 'Inbound',
    callStatus: 'Completed',
    callStartTime: '2024-07-08T10:30:00.000Z',
    callEndTime: '2024-07-08T10:45:00.000Z',
    duration: 900,
    callPurpose: 'Product inquiry and pricing information',
    notes: 'Customer interested in enterprise package. Sent follow-up email with pricing details.',
    followUpRequired: 'Yes',
  },
  {
    id: CALL_DATA_SEED_IDS.ID_2,
    callerName: 'Maria Garcia',
    phoneNumber: '******-0456',
    callType: 'Outbound',
    callStatus: 'Completed',
    callStartTime: '2024-07-09T14:15:00.000Z',
    callEndTime: '2024-07-09T14:35:00.000Z',
    duration: 1200,
    callPurpose: 'Follow-up on previous meeting',
    notes: 'Discussed implementation timeline. Customer ready to proceed with contract.',
    followUpRequired: 'No',
  },
  {
    id: CALL_DATA_SEED_IDS.ID_3,
    callerName: 'James Wilson',
    phoneNumber: '******-0789',
    callType: 'Inbound',
    callStatus: 'Missed',
    callStartTime: '2024-07-10T09:20:00.000Z',
    callEndTime: null,
    duration: 0,
    callPurpose: 'Technical support request',
    notes: 'Missed call - customer left voicemail about login issues. Need to call back.',
    followUpRequired: 'Yes',
  },
  {
    id: CALL_DATA_SEED_IDS.ID_4,
    callerName: 'Jennifer Lee',
    phoneNumber: '******-0321',
    callType: 'Outbound',
    callStatus: 'Completed',
    callStartTime: '2024-07-11T16:00:00.000Z',
    callEndTime: '2024-07-11T16:25:00.000Z',
    duration: 1500,
    callPurpose: 'Sales presentation',
    notes: 'Presented new features to existing customer. They are interested in upgrading their plan.',
    followUpRequired: 'Yes',
  },
  {
    id: CALL_DATA_SEED_IDS.ID_5,
    callerName: 'Michael Brown',
    phoneNumber: '******-0654',
    callType: 'Inbound',
    callStatus: 'Completed',
    callStartTime: '2024-07-12T11:45:00.000Z',
    callEndTime: '2024-07-12T12:10:00.000Z',
    duration: 1500,
    callPurpose: 'Complaint resolution',
    notes: 'Customer had billing issue. Resolved by adjusting account and providing credit.',
    followUpRequired: 'No',
  },
  {
    id: CALL_DATA_SEED_IDS.ID_6,
    callerName: 'Susan Davis',
    phoneNumber: '******-0987',
    callType: 'Outbound',
    callStatus: 'No Answer',
    callStartTime: '2024-07-13T13:30:00.000Z',
    callEndTime: null,
    duration: 0,
    callPurpose: 'Contract renewal discussion',
    notes: 'No answer - will try again tomorrow. Contract expires next month.',
    followUpRequired: 'Yes',
  },
];
