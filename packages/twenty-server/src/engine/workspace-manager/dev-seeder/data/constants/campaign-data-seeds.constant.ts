type CampaignDataSeed = {
  id: string;
  campaignName: string;
  campaignType: string;
  status: string;
  startDate: string;
  endDate: string;
  budget: number;
  targetAudienceSize: number;
  channel: string;
  description: string;
  campaignManager: string;
  expectedRoi: number;
};

export const CAMPAIGN_DATA_SEED_COLUMNS: (keyof CampaignDataSeed)[] = [
  'id',
  'campaignName',
  'campaignType',
  'status',
  'startDate',
  'endDate',
  'budget',
  'targetAudienceSize',
  'channel',
  'description',
  'campaignManager',
  'expectedRoi',
];

export const CAMPAIGN_DATA_SEED_IDS = {
  ID_1: '50505050-0001-0000-0000-000000000001',
  ID_2: '50505050-0002-0000-0000-000000000002',
  ID_3: '50505050-0003-0000-0000-000000000003',
  ID_4: '50505050-0004-0000-0000-000000000004',
  ID_5: '50505050-0005-0000-0000-000000000005',
  ID_6: '50505050-0006-0000-0000-000000000006',
};

export const CAMPAIGN_DATA_SEEDS: CampaignDataSeed[] = [
  {
    id: CAMPAIGN_DATA_SEED_IDS.ID_1,
    campaignName: 'Summer Product Launch',
    campaignType: 'Product Launch',
    status: 'Active',
    startDate: '2024-07-01T00:00:00.000Z',
    endDate: '2024-08-31T23:59:59.000Z',
    budget: 50000.00,
    targetAudienceSize: 100000,
    channel: 'Digital Marketing',
    description: 'Comprehensive campaign to launch our new summer product line with focus on social media and email marketing',
    campaignManager: 'Sarah Marketing',
    expectedRoi: 250.0,
  },
  {
    id: CAMPAIGN_DATA_SEED_IDS.ID_2,
    campaignName: 'Back to School Promotion',
    campaignType: 'Promotional',
    status: 'Planning',
    startDate: '2024-08-15T00:00:00.000Z',
    endDate: '2024-09-15T23:59:59.000Z',
    budget: 25000.00,
    targetAudienceSize: 50000,
    channel: 'Email Marketing',
    description: 'Targeted email campaign offering discounts for students and educators',
    campaignManager: 'Mike Campaigns',
    expectedRoi: 180.0,
  },
  {
    id: CAMPAIGN_DATA_SEED_IDS.ID_3,
    campaignName: 'Holiday Season Campaign',
    campaignType: 'Seasonal',
    status: 'Draft',
    startDate: '2024-11-01T00:00:00.000Z',
    endDate: '2024-12-31T23:59:59.000Z',
    budget: 75000.00,
    targetAudienceSize: 200000,
    channel: 'Multi-channel',
    description: 'Major holiday campaign across all channels including TV, digital, and print advertising',
    campaignManager: 'Jennifer Ads',
    expectedRoi: 300.0,
  },
  {
    id: CAMPAIGN_DATA_SEED_IDS.ID_4,
    campaignName: 'Customer Retention Program',
    campaignType: 'Retention',
    status: 'Active',
    startDate: '2024-06-01T00:00:00.000Z',
    endDate: '2024-12-31T23:59:59.000Z',
    budget: 30000.00,
    targetAudienceSize: 15000,
    channel: 'Email & SMS',
    description: 'Ongoing campaign to increase customer loyalty through personalized offers and communications',
    campaignManager: 'David Retention',
    expectedRoi: 400.0,
  },
  {
    id: CAMPAIGN_DATA_SEED_IDS.ID_5,
    campaignName: 'Brand Awareness Drive',
    campaignType: 'Brand Awareness',
    status: 'Completed',
    startDate: '2024-04-01T00:00:00.000Z',
    endDate: '2024-06-30T23:59:59.000Z',
    budget: 40000.00,
    targetAudienceSize: 500000,
    channel: 'Social Media',
    description: 'Large-scale social media campaign to increase brand recognition and engagement',
    campaignManager: 'Lisa Social',
    expectedRoi: 150.0,
  },
  {
    id: CAMPAIGN_DATA_SEED_IDS.ID_6,
    campaignName: 'Lead Generation Campaign',
    campaignType: 'Lead Generation',
    status: 'Paused',
    startDate: '2024-05-15T00:00:00.000Z',
    endDate: '2024-07-15T23:59:59.000Z',
    budget: 20000.00,
    targetAudienceSize: 25000,
    channel: 'Google Ads',
    description: 'Targeted Google Ads campaign to generate qualified leads for sales team',
    campaignManager: 'Robert Leads',
    expectedRoi: 220.0,
  },
];
