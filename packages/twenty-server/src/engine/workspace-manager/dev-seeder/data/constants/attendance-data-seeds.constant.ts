type AttendanceDataSeed = {
  id: string;
  attendeeName: string;
  eventName: string;
  attendanceStatus: string;
  checkInTime: string | null;
  checkOutTime: string | null;
  notes: string;
  attendanceDate: string;
};

export const ATTENDANCE_DATA_SEED_COLUMNS: (keyof AttendanceDataSeed)[] = [
  'id',
  'attendeeName',
  'eventName',
  'attendanceStatus',
  'checkInTime',
  'checkOutTime',
  'notes',
  'attendanceDate',
];

export const ATTENDANCE_DATA_SEED_IDS = {
  ID_1: '30303030-0001-0000-0000-000000000001',
  ID_2: '30303030-0002-0000-0000-000000000002',
  ID_3: '30303030-0003-0000-0000-000000000003',
  ID_4: '30303030-0004-0000-0000-000000000004',
  ID_5: '30303030-0005-0000-0000-000000000005',
  ID_6: '30303030-0006-0000-0000-000000000006',
};

export const ATTENDANCE_DATA_SEEDS: AttendanceDataSeed[] = [
  {
    id: ATTENDANCE_DATA_SEED_IDS.ID_1,
    attendeeName: '<PERSON>',
    eventName: 'Weekly Team Meeting',
    attendanceStatus: 'Present',
    checkInTime: '2024-07-08T09:00:00.000Z',
    checkOutTime: '2024-07-08T10:00:00.000Z',
    notes: 'Participated actively in discussions',
    attendanceDate: '2024-07-08T09:00:00.000Z',
  },
  {
    id: ATTENDANCE_DATA_SEED_IDS.ID_2,
    attendeeName: 'Sarah Johnson',
    eventName: 'Product Launch Event',
    attendanceStatus: 'Present',
    checkInTime: '2024-07-10T14:00:00.000Z',
    checkOutTime: '2024-07-10T17:00:00.000Z',
    notes: 'Helped with event setup and coordination',
    attendanceDate: '2024-07-10T14:00:00.000Z',
  },
  {
    id: ATTENDANCE_DATA_SEED_IDS.ID_3,
    attendeeName: 'Mike Davis',
    eventName: 'Training Workshop',
    attendanceStatus: 'Late',
    checkInTime: '2024-07-12T09:15:00.000Z',
    checkOutTime: '2024-07-12T12:00:00.000Z',
    notes: 'Arrived 15 minutes late due to traffic',
    attendanceDate: '2024-07-12T09:00:00.000Z',
  },
  {
    id: ATTENDANCE_DATA_SEED_IDS.ID_4,
    attendeeName: 'Emily Wilson',
    eventName: 'Client Presentation',
    attendanceStatus: 'Absent',
    checkInTime: null,
    checkOutTime: null,
    notes: 'Called in sick, unable to attend',
    attendanceDate: '2024-07-15T10:00:00.000Z',
  },
  {
    id: ATTENDANCE_DATA_SEED_IDS.ID_5,
    attendeeName: 'David Brown',
    eventName: 'Company All-Hands',
    attendanceStatus: 'Present',
    checkInTime: '2024-07-18T13:30:00.000Z',
    checkOutTime: '2024-07-18T15:30:00.000Z',
    notes: 'Asked several questions during Q&A session',
    attendanceDate: '2024-07-18T13:30:00.000Z',
  },
  {
    id: ATTENDANCE_DATA_SEED_IDS.ID_6,
    attendeeName: 'Lisa Anderson',
    eventName: 'Safety Training',
    attendanceStatus: 'Excused',
    checkInTime: null,
    checkOutTime: null,
    notes: 'Pre-approved absence for medical appointment',
    attendanceDate: '2024-07-20T11:00:00.000Z',
  },
];
