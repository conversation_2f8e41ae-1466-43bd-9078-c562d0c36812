import { NumberDataType } from 'src/engine/metadata-modules/field-metadata/interfaces/field-metadata-settings.interface';
import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const CAMPAIGN_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Campaign Name',
    name: 'campaignName',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Campaign Type',
    name: 'campaignType',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Status',
    name: 'status',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Start Date',
    name: 'startDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'End Date',
    name: 'endDate',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Budget',
    name: 'budget',
    settings: {
      dataType: NumberDataType.FLOAT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Target Audience Size',
    name: 'targetAudienceSize',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Channel',
    name: 'channel',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Description',
    name: 'description',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Campaign Manager',
    name: 'campaignManager',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Expected ROI (%)',
    name: 'expectedRoi',
    settings: {
      dataType: NumberDataType.FLOAT,
      type: 'number',
    },
  } as FieldMetadataSeed,
];
