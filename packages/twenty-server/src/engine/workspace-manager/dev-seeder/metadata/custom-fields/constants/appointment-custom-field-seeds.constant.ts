import { NumberDataType } from 'src/engine/metadata-modules/field-metadata/interfaces/field-metadata-settings.interface';
import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const APPOINTMENT_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Description',
    name: 'description',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Start Time',
    name: 'startTime',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'End Time',
    name: 'endTime',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Duration (minutes)',
    name: 'duration',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Location',
    name: 'location',
    settings: {
      displayedMaxRows: 2,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Appointment Type',
    name: 'appointmentType',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Mode',
    name: 'mode',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Status',
    name: 'status',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Check-in Time',
    name: 'checkinTime',
    settings: {},
  } as FieldMetadataSeed,
];
