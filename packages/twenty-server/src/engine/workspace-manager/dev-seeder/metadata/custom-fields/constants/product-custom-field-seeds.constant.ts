import { FieldMetadataType } from 'src/engine/metadata-modules/field-metadata/field-metadata.entity';
import { FieldMetadataDTO } from 'src/engine/metadata-modules/field-metadata/dtos/field-metadata.dto';
import { NumberDataType } from 'src/engine/metadata-modules/field-metadata/interfaces/field-metadata-settings.interface';
import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';

export const PRODUCT_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'SKU',
    name: 'sku',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataDTO<FieldMetadataType.TEXT>,
  {
    type: FieldMetadataType.TEXT,
    label: 'Description',
    name: 'description',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataDTO<FieldMetadataType.TEXT>,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Price',
    name: 'price',
    settings: {
      dataType: NumberDataType.FLOAT,
      decimals: 2,
      type: 'number',
    },
  } as FieldMetadataDTO<FieldMetadataType.NUMBER>,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Stock Quantity',
    name: 'stockQuantity',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataDTO<FieldMetadataType.NUMBER>,
  {
    type: FieldMetadataType.TEXT,
    label: 'Category',
    name: 'category',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataDTO<FieldMetadataType.TEXT>,
  {
    type: FieldMetadataType.BOOLEAN,
    label: 'Is Active',
    name: 'isActive',
    settings: {},
  } as FieldMetadataDTO<FieldMetadataType.BOOLEAN>,
];
