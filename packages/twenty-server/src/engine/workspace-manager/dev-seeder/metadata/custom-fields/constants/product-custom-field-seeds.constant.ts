import { NumberDataType } from 'src/engine/metadata-modules/field-metadata/interfaces/field-metadata-settings.interface';
import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const PRODUCT_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'SKU',
    name: 'sku',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Description',
    name: 'description',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Price',
    name: 'price',
    settings: {
      dataType: NumberDataType.FLOAT,
      decimals: 2,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.NUMBER,
    label: 'Stock Quantity',
    name: 'stockQuantity',
    settings: {
      dataType: NumberDataType.INT,
      type: 'number',
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Category',
    name: 'category',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.BOOLEAN,
    label: 'Is Active',
    name: 'isActive',
    settings: {},
  } as FieldMetadataSeed,
];
