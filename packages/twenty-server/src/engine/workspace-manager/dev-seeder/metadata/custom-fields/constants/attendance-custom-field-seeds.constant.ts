import { FieldMetadataSeed } from 'src/engine/workspace-manager/dev-seeder/metadata/types/field-metadata-seed.type';
import { FieldMetadataType } from 'twenty-shared/types';

export const ATTENDANCE_CUSTOM_FIELD_SEEDS: FieldMetadataSeed[] = [
  {
    type: FieldMetadataType.TEXT,
    label: 'Attendee Name',
    name: 'attendeeName',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Event Name',
    name: 'eventName',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Attendance Status',
    name: 'attendanceStatus',
    settings: {
      displayedMaxRows: 1,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Check-in Time',
    name: 'checkInTime',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Check-out Time',
    name: 'checkOutTime',
    settings: {},
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.TEXT,
    label: 'Notes',
    name: 'notes',
    settings: {
      displayedMaxRows: 3,
    },
  } as FieldMetadataSeed,
  {
    type: FieldMetadataType.DATE_TIME,
    label: 'Attendance Date',
    name: 'attendanceDate',
    settings: {},
  } as FieldMetadataSeed,
];
