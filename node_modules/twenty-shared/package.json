{"name": "twenty-shared", "main": "dist/twenty-shared.cjs.js", "module": "dist/twenty-shared.esm.js", "license": "AGPL-3.0", "scripts": {"build": "preconstruct build"}, "engines": {"node": "^22.12.0", "npm": "please-use-yarn", "yarn": "^4.0.2"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@preconstruct/cli": "^2.8.12", "@prettier/sync": "^0.5.2", "@types/babel__preset-env": "^7", "babel-plugin-module-resolver": "^5.0.2", "glob": "^11.0.1", "tsx": "^4.19.3"}, "dependencies": {"@sniptt/guards": "^0.2.0", "libphonenumber-js": "^1.10.26", "zod": "3.23.8"}, "preconstruct": {"tsconfig": "tsconfig.lib.json", "entrypoints": ["./index.ts", "./constants/index.ts", "./testing/index.ts", "./translations/index.ts", "./types/index.ts", "./utils/index.ts", "./workspace/index.ts"]}, "files": ["dist", "constants", "testing", "translations", "types", "utils", "workspace"]}