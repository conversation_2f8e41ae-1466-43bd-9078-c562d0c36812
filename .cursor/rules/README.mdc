---
description: 
globs: 
alwaysApply: false
---
# Twenty Development Rules

This directory contains Twenty's development guidelines and best practices in the modern Cursor Rules format (MDC). These rules are automatically applied based on file patterns and provide context-aware guidance to AI assistants.

## Rules Overview

### Core Guidelines
- **architecture.mdc** - Project overview, technology stack, and infrastructure setup (Always Applied)
- **nx-rules.mdc** - Nx workspace guidelines and best practices (Auto-attached to Nx files)

### Code Quality
- **typescript-guidelines.mdc** - TypeScript best practices and conventions (Auto-attached to .ts/.tsx files)
- **code-style.mdc** - General coding standards and style guide (Auto-attached to code files)
- **file-structure.mdc** - File and directory organization patterns (Auto-attached to config files)

### React Development
- **react-general-guidelines.mdc** - Core React development principles (Auto-attached to React files)
- **react-state-management.mdc** - State management approaches with Recoil (Auto-attached to state files)

### Testing & Quality
- **testing-guidelines.mdc** - Testing strategies and best practices (Auto-attached to test files)

### Internationalization
- **translations.mdc** - Translation workflow and i18n setup (Auto-attached to locale files)

## How Rules Work

### Automatic Attachment
Rules are automatically included in your AI context based on file patterns (globs). When you work on TypeScript files, the TypeScript guidelines are automatically loaded.

### Manual Reference
You can manually reference any rule using the `@ruleName` syntax:
- `@nx-rules` - Include Nx-specific guidance
- `@react-general-guidelines` - Load React best practices
- `@testing-guidelines` - Get testing recommendations

### Rule Types Used
- **Always Applied** - Loaded in every context (architecture.mdc, README.mdc)  
- **Auto Attached** - Loaded when matching file patterns are referenced
- **Agent Requested** - Available for AI to include when relevant
- **Manual** - Only included when explicitly mentioned

## Development Commands

### Frontend Commands
```bash
# Testing
npx nx test twenty-front                    # Run unit tests
npx nx storybook:build twenty-front         # Build Storybook
npx nx storybook:serve-and-test:static     # Run Storybook tests

# Development
npx nx lint twenty-front                    # Run linter
npx nx typecheck twenty-front               # Type checking
npx nx run twenty-front:graphql:generate    # Generate GraphQL types
```

### Backend Commands
```bash
# Database
npx nx database:reset twenty-server         # Reset database
npx nx run twenty-server:database:init:prod # Initialize database
npx nx run twenty-server:database:migrate:prod # Run migrations

# Development
npx nx run twenty-server:start             # Start the server
npx nx run twenty-server:lint              # Run linter (add --fix to auto-fix)
npx nx run twenty-server:typecheck         # Type checking
npx nx run twenty-server:test              # Run unit tests
npx nx run twenty-server:test:integration:with-db-reset # Run integration tests

# Migrations
npx nx run twenty-server:typeorm migration:generate src/database/typeorm/core/migrations/[name] -d src/database/typeorm/core/core.datasource.ts

# Workspace
npx nx run twenty-server:command workspace:sync-metadata -f  # Sync metadata
```

## Usage Guidelines

### For Developers
- Rules are automatically applied based on file context
- Check rule descriptions to understand when they're activated
- Use manual references (`@ruleName`) for additional context
- Keep rules updated as the codebase evolves

### For AI Assistants
- Rules provide consistent guidance across conversations
- Use rule context to maintain coding standards
- Reference specific rules when making recommendations
- Apply rule principles in code suggestions and reviews

## Contributing to Rules

### Adding New Rules
1. Create a new `.mdc` file in this directory
2. Include proper metadata headers with description and globs
3. Write clear, actionable guidelines with examples
4. Test the rule with relevant file patterns
5. Update this README if needed

### Updating Existing Rules
1. Modify the rule content while preserving metadata
2. Test changes with affected file patterns
3. Ensure consistency with other rules
4. Update examples and best practices as needed

## Rule Format Reference

Each rule file uses the MDC format with metadata:

```markdown
---
description: Brief description of the rule's purpose
globs: ["**/*.ts", "**/*.tsx"]  # File patterns for auto-attachment
alwaysApply: false              # Whether to always include this rule
---

# Rule Title

Rule content in Markdown format...
```

## Migration from Legacy Format

The rules have been migrated from the legacy `.md` format to the modern `.mdc` format, providing:
- Better context awareness through file pattern matching
- Improved organization with metadata headers
- More flexible rule application strategies
- Enhanced integration with Cursor's AI features

For the most up-to-date version of these guidelines, always refer to the files in this directory.
